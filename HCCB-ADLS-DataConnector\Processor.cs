﻿using System.Data;
using System.Net.Http.Headers;
using System.Text;
using Microsoft.Azure.WebJobs;
using Microsoft.Identity.Client;
using Azure.Storage.Files.DataLake;
using HCCB_ADLS_DataConnector.Configuration;
using HCCB_ADLS_DataConnector.Helper;
using Parquet;
using Parquet.Schema;
using EFCore.BulkExtensions;
using HCCB_ADLS.DbStorage.MasterRepositories.HccbModels;
using HCCB_ADLS.DbStorage.DbContexts;
using HCCB_ADLS.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using HCCB_ADLS.Core.APIModels;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using Core.Loggers;
using Library.ResponseHelpers;
using HCCB_ADLS_DataConnector.Models;
using Polly;
using Polly.Retry;
using Core.APIHelpers;
using Core.Helpers;
using Core.Abstracts;
using Core.Models.HccbDbModels;
using Core.Models;
using Core.Repositories;
using Library.CommonHelpers.Extensions;

namespace HCCB_ADLS_DataConnector
{
    public class Processor : ProcessorBaseIntegrationLogs
    {
        private const string SlackMessageHeader = "ADLS Non Fa Invoice Processor";
        private const string LogsDirectory = "Logs";
        private const int _batchSizeForExtAPI = 2000;
        private const int _batchSizeForParquet = 100000;

        private static readonly AsyncRetryPolicy anyExceptionRetryPolicy = Policy
                    .Handle<Exception>()
                    .WaitAndRetryAsync(3, retryNumber => TimeSpan.FromSeconds(retryNumber * 2));

        private readonly HttpClient externalHttpClient;
        private readonly WritableHccbDbContext _writableHccbDb;
        private readonly IOutletRepository outletRepository;
        private readonly IDistributorRepository distributorRepository;
        private readonly HttpRequestHelperV2 _httpRequestHelper;
        private readonly WritableHccbDbContext _writableHccbDbContext;
        private readonly SqlQueryHelper _sqlQueryHelper;
        private readonly SemaphoreSlim semaphore;

        protected override string LogHeader { get; set; }
        protected override List<IntegratationSummary> _summary { get; set; } = new List<IntegratationSummary>();
        protected override List<EntityEnum> IntegrationEntities => new List<EntityEnum> { EntityEnum.Invoice };

        public Processor(IServiceProvider serviceProvider,
            IHttpClientFactory httpClientFactory,
            WritableHccbDbContext writableHccbDb,
            IOutletRepository outletRepository,
            IDistributorRepository distributorRepository,
            ISlackLogHelper slackLogHelper,
            FileLogger fileLogger,
            HttpRequestHelperV2 httpRequestHelper,
            WritableHccbDbContext writableHccbDbContext,
            SqlQueryHelper sqlQueryHelper,
            IHccbIntegrationLogsRepository hccbIntegrationRepository)
            : base(
            fileLogger: fileLogger,
            slackLogHelper: slackLogHelper,
            hccbIntegrationRepository: hccbIntegrationRepository)
        {
            externalHttpClient = httpClientFactory.CreateClient("ResilientExternalApiClient");
            _writableHccbDb = writableHccbDb;
            this.outletRepository = outletRepository;
            this.distributorRepository = distributorRepository;
            _httpRequestHelper=httpRequestHelper;
            this._writableHccbDbContext=writableHccbDbContext;
            this._sqlQueryHelper=sqlQueryHelper;
            semaphore = new SemaphoreSlim(5); // Limiting to 5 concurrent requests, adjust as needed
        }
        protected override string GetProcessorName() => SlackMessageHeader;
        protected override string GetChannelId() => Dependencies.ChannelId;
        protected override long GetCompanyId() => companyId; // Will be set from queue message
        protected override IntegrationEnum GetIntegrationEnum() => IntegrationEnum.ADLS; 
        protected override bool CompressLogFiles => true;
        private long companyId = 0;
        private long integrationLogId = 0;
        private AdlsInvoiceQueueData? queueRequest = null;
        private FileInfo? logFile = null;

        public async Task ProcessNonFaInvoiceQueue([QueueTrigger(Dependencies.processorNonFaInvoiceQueue)] string queueMessagePayload)
        {
            queueRequest = JsonConvert.DeserializeObject<AdlsInvoiceQueueData>(queueMessagePayload);
            // Call the overridden Process method
            companyId = queueRequest.CompanyId;
            await Process(queueRequest);
        }

        protected override (string, FileInfo) InitializeLogFile()
        {
            LogHeader = $"[{SlackMessageHeader}]: {queueRequest.CompanyId} - {queueRequest.FileName}: ";
            var logFileName = $"{Path.GetTempPath()}{Path.DirectorySeparatorChar}{LogsDirectory}{Path.DirectorySeparatorChar}{queueRequest.FileName}_logs_{Guid.NewGuid()}.txt";
            logFile = new FileInfo(logFileName);
            return (logFileName, logFile);
        }
        
        protected override async Task _Process(params object?[] args)
        {
            integrationLogId = GetIntegrationLog(IntegrationEntities[0]).Id;
            var result = await SaveAndProcessData(queueRequest, fromSas: queueRequest.IsfromSAS);
            var totalInvoices = result.successFulRecords + result.failedRecords;
            _summary.Add(new IntegratationSummary
            {
                TotalReceived = totalInvoices,
                Successful = result.successFulRecords,
                Failed = result.failedRecords,
                Ignored = 0
            });
            
            if (totalInvoices > 0)
            {
                await _slackLogHelper.SendCustomSlackMessage(LogHeader, true, $" Log File: {logFile.Name}", totalInvoices, result.successFulRecords, result.failedRecords, null);
            }
            else if (totalInvoices < 0)
            {
                await _slackLogHelper.SendCustomSlackMessage(LogHeader, false, $"No valid outlet legacy codes found to process in ADLS invoice processor for file {queueRequest.FileName} " + $" Log File: {logFile.Name}");
            }
            else
            {
                await _slackLogHelper.SendCustomSlackMessage(LogHeader, false, "Unhandled Exception Occured! Or No Valid Invoices Found. No data about failure percentage could be gathered!" + $" Log File: {logFile.Name}");
            }
        }
        
        protected override void _ValidateArguments(params object?[] args)
        {
        }

        private async Task<(int successFulRecords, int failedRecords)> SaveAndProcessData(AdlsInvoiceQueueData queueData,
                     string fileExtension = "parquet", bool fromSas = false)
        {
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{queueData.ExternalAPIUserName}:{queueData.ExternalAPIPassword}"));
            externalHttpClient.BaseAddress = new Uri(queueData.ExternalApiBaseAddress);
            externalHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", credentials);
            externalHttpClient.DefaultRequestHeaders.Add("ContentType", "application/json");

            if (!queueData.SkipFileRead)
            {
                fileLogger.WriteLine($"Skippin File Read");
                await _slackLogHelper.SendMessageToSlack($"{LogHeader} Skipping file Read: {queueData.DirectoryPath}/{queueData.FileName}. Timestamp: {DateTime.UtcNow}");
                return await SaveDataAndCallExternalAPI(null, queueData);
            }
            else
            {
                var serviceClient = await GetAdlsClient(fromSas, queueData.AccountName);

                var fileSystemClient = serviceClient.GetFileSystemClient(queueData.FileSystemName);

                var directoryClient = fileSystemClient.GetDirectoryClient(queueData.DirectoryPath);

                var fileClient = directoryClient.GetFileClient($"{queueData.FileName}.{fileExtension}");

                fileLogger.WriteLine($"Reading file : {fileClient.Path}");
                await _slackLogHelper.SendMessageToSlack($"{LogHeader} HCCB-ADLS-NonFaInvoice-DataConnector Processor Reading file : {fileClient.Path}. Timestamp: {DateTime.UtcNow}");

                return await SaveDataAndCallExternalAPI(fileClient, queueData);
            }
        }
        private async Task<(int successFulRecords, int failedRecords)> SaveDataAndCallExternalAPI(DataLakeFileClient? fileClient, AdlsInvoiceQueueData queueData)
        {
            var companyId = queueData.CompanyId;
            var allActiveOutlets = await outletRepository.GetAllOutlets(companyId);
            var allActiveDistributors = await distributorRepository.GetDistributors(companyId);
            var outletLegacyErpDict = allActiveOutlets.Where(p => !string.IsNullOrWhiteSpace(p.AttributeText4)).GroupBy(p => p.AttributeText4).ToDictionary(p => p.Key, p => p.First().ERPId);
            var distributorLegacyErpDict = allActiveDistributors.Where(p => !string.IsNullOrWhiteSpace(p.AttributeText2)).GroupBy(p => p.AttributeText2).ToDictionary(p => p.Key, p => p.First().ERPId);

            if (fileClient != null && !queueData.SkipFileRead)
            {
                var validOutletCodes = queueData.ConsiderSpecifiedOutletLegacyCodes ? new HashSet<string>(queueData.OutletLegacyCodes != null ? queueData.OutletLegacyCodes : [])
                                       : [.. allActiveOutlets.Where(p => !string.IsNullOrWhiteSpace(p.AttributeText4)).Select(p => p.AttributeText4).Distinct()];
                if (validOutletCodes.Count == 0)
                {
                    fileLogger.WriteLine($"failed: no valid outlet legacy codes found to process. Skipping File Read");
                    await _slackLogHelper.SendMessageToSlack($"{LogHeader} Failed: no valid outlet legacy codes found to process for file {queueData.FileName}\n" +
                        $"Skipping File Read");
                }
                else
                {
                    await ProcessParquetFile(fileClient, _batchSizeForParquet, companyId, validOutletCodes);
                }
            }
            return await ProcessDataAndCallExtAPI(companyId, queueData.ExternalApiEndPoint, outletLegacyErpDict, distributorLegacyErpDict);
        }

        private async Task ProcessParquetFile(DataLakeFileClient fileClient, int batchSize, long companyId, HashSet<string> validOutletCodes)
        {
            long skipRows = 0;
            fileLogger.WriteLine($"Begining Read ....");
            var response = await fileClient.ReadAsync();
            Stream contentStream = response.Value.Content;
            int recordsInfile = 0;

            var safeFileName = companyId + "_" +  string.Join("_", $"{fileClient.Path}".Split(Path.GetInvalidFileNameChars()));
            var file = new FileInfo($"{Path.GetTempPath()}{Path.DirectorySeparatorChar}{safeFileName}");
            using (var fileStream = file.OpenWrite())
            {
                contentStream.CopyTo(fileStream);
                await fileStream.FlushAsync();
            }
            await _slackLogHelper.SendLogAndFile(file, $"File Downloaded for {fileClient.Name}", GetChannelId());
            try
            {
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    using (var copiedFileStream = file.OpenRead())
                    {
                        await copiedFileStream.CopyToAsync(memoryStream);
                    }
                    memoryStream.Seek(0, SeekOrigin.Begin);
                    var options = new ParquetOptions { TreatByteArrayAsString = true };
                    using (ParquetReader parquetReader = await ParquetReader.CreateAsync(memoryStream, options))
                    {
                        DataField[] dataFields = parquetReader.Schema.GetDataFields();

                        fileLogger.WriteLine($"file {fileClient.Path} has total groups {parquetReader.RowGroupCount}");

                        for (int i = 0; i < parquetReader.RowGroupCount; i++)
                        {
                            using (ParquetRowGroupReader groupReader = parquetReader.OpenRowGroupReader(i))
                            {
                                var rowcount = groupReader.RowCount;
                                fileLogger.WriteLine($"file {fileClient.Path} group {i + 1} has total rows {rowcount}");

                                skipRows = 0;
                                while (skipRows < rowcount)
                                {
                                    long remainingRows = rowcount - skipRows;
                                    long rowsToRead = Math.Min(batchSize, remainingRows);

                                    using (DataTable dataTable = new DataTable())
                                    {
                                        foreach (var col in dataFields)
                                        {
                                            dataTable.Columns.Add(col.Name, Nullable.GetUnderlyingType(col.ClrType) ?? col.ClrType);
                                        }

                                        for (int rowIndex = 0; rowIndex < rowsToRead; rowIndex++)
                                        {
                                            DataRow dataRow = dataTable.NewRow();
                                            dataTable.Rows.Add(dataRow);
                                        }

                                        foreach (var col in dataFields)
                                        {
                                            var column = await groupReader.ReadColumnAsync(col);

                                            Array rows = column.Data;
                                            object[] rowData = new object[rowsToRead];
                                            Array.Copy(rows, skipRows, rowData, 0, rowsToRead);

                                            for (int j = 0; j < rowData.Length; j++)
                                            {
                                                dataTable.Rows[j][col.Name] = rowData[j] == null ? DBNull.Value : rowData[j];
                                            }
                                        }

                                        await SaveDataInInvoiceTable(dataTable, fileClient.Path, companyId, validOutletCodes);
                                        recordsInfile += dataTable.Rows.Count;
                                    }
                                    skipRows += rowsToRead;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                fileLogger.WriteLine($"An error occurred While processing parquet File Continuing to process data saved in staging table:\n{ex}");
                await _slackLogHelper.SendMessageToSlack($"An error occurred While processing parquet File Continuing to process data saved in staging table. See log file at the end for more details");
            }
            finally
            {
                file.Delete();
            }
        }

        private async Task SaveDataInInvoiceTable(DataTable dataTablemain, string filePath, long companyId, HashSet<string> activeOutletCodes) //  confirm if we need to store path
        {
            try
            {
                var filteredData = dataTablemain;
                List<ADLSInvoice> invoices = new List<ADLSInvoice>();

                if (filteredData.Rows.Count > 0)
                {
                    foreach (DataRow row in filteredData.Rows)
                    {
                        ADLSInvoice invoice = new ADLSInvoice
                        {
                            distributor_legacy_code = row["distributor_legacy_code"].ToString(),
                            outlet_legacy_code = row["outlet_legacy_code"].ToString(),
                            orderno = row["orderno"].ToString(),
                            invoice_no = row["invoice_no"].ToString() +"_" + row["distributor_legacy_code"].ToString(),
                            invoice_line_no = long.Parse(row["invoice_line_no"].ToString()),
                            invoice_date = DateTime.Parse(row["invoice_date"].ToString()),
                            invoice_time = TimeSpan.Parse(row["invoice_time"].ToString()), // Assuming you'll set the time later or it's not required
                            material = row["material"].ToString(),
                            totalqty_ea = int.Parse(row["totalqty_ea"].ToString()),
                            base_price = double.Parse(row["base_price"].ToString()),
                            mer_no = row["mer_no"].ToString(),
                            discount_amount = double.Parse(row["discount_amount"].ToString()),
                            line_amount = double.Parse(row["line_amount"].ToString()),
                            line_tax_on_discount = double.Parse(row["line_tax_on_discount"].ToString()),
                            sale_type = int.Parse(row["sale_type"].ToString()),
                            cancelled_invoice_flag = row["cancelled_invoice_flag"].ToString() == "N" ? false : true,
                            ind_sgst_tax_rate = double.Parse(row["ind_sgst_tax_rate"].ToString()),
                            ind_cgst_tax_rate = double.Parse(row["ind_cgst_tax_rate"].ToString()),
                            ind_igst_tax_rate = double.Parse(row["ind_igst_tax_rate"].ToString()),
                            ind_utgst_tax_rate = double.Parse(row["ind_utgst_tax_rate"].ToString()),
                            ind_cess_tax_rate = double.Parse(row["ind_cess_tax_rate"].ToString()),
                            IsProcessed = false,
                            companyid = companyId,
                            createdAt = DateTime.UtcNow,
                            updatedAt = null,
                            creationcontext = "hccb-adls-non-fa-invoice-processor",
                            FilePath = filePath,
                            FAProcessAttempts = 0,
                            FAProcessFailures = ""
                        };

                        invoices.Add(invoice);
                    }

                    invoices = invoices.Where(s => activeOutletCodes.Contains(s.outlet_legacy_code)).ToList();

                    if (invoices.Count > 0)
                    {
                        await anyExceptionRetryPolicy.ExecuteAsync(async () => await SaveinDb(invoices));
                    }
                }
            }
            catch (Exception ex)
            {
                fileLogger.WriteLine($"An error occurred while saving data in staging Table. Continuing to process further batches: {ex}");
            }
        }

        private async Task SaveinDb(List<ADLSInvoice> invoices)
        {
            await _writableHccbDb.Database.CreateExecutionStrategy().Execute(async () =>
            {
                using var transaction = await _writableHccbDb.Database.BeginTransactionAsync();
                try
                {
                    await _writableHccbDb.BulkInsertAsync(invoices, new BulkConfig
                    {
                        BatchSize = 5000,
                        UseTempDB = true
                    });

                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });
        }


        private async Task<(int successFulRecords, int failedRecords)> ProcessDataAndCallExtAPI(long companyId, string apiEndPoint, Dictionary<string, string> outletLegacyErpDict, Dictionary<string, string> distributorLegacyErpDict)
        {
            var dbDataToBeProcessed = new List<ADLSInvoice>();

            const int maxAttempts = 3;
            var invoiceQueryable = _writableHccbDbContext.ADLSInvoice
                .Where(d => d.companyid == companyId && !d.IsProcessed && !d.cancelled_invoice_flag && d.FAProcessAttempts < maxAttempts);

            var distinctInvoices = await invoiceQueryable.Select(d => d.invoice_no).Distinct().ToListAsync();

            var countOfData = distinctInvoices.Count;
            const int batchSize = 10000;
            const int deleteBatchSize = 1000;
            var numberOfBatches = (int)Math.Ceiling((double)countOfData / (double)batchSize);

            fileLogger.WriteLine($"Found {countOfData} invoices in {numberOfBatches} batches");
            var outletLegacyUserERPDict = (await outletRepository.GetOutletLegacyCodeAndUserERP(companyId))
                                      .Where(p => !string.IsNullOrEmpty(p.OutletLegacyCode))
                                      .GroupBy(p => p.OutletLegacyCode).ToDictionary(p => p.Key, p => p.First().UserERPId);

            var responses = new List<(int successCount, int failureCount)>(numberOfBatches); 

            for (int batchIndex = 0; batchIndex < numberOfBatches; batchIndex++)
            {
                fileLogger.WriteLine($"Fetching data for batch {batchIndex + 1}/{numberOfBatches}");
                var skip = batchSize * batchIndex;

                var invoicesInThisBatch = distinctInvoices.Skip(skip).Take(batchSize).ToList();

                dbDataToBeProcessed = await _writableHccbDb.ADLSInvoice.Where(d => d.companyid == companyId && invoicesInThisBatch.Contains(d.invoice_no)).ToListAsync();

                fileLogger.WriteLine($"Processing batch {batchIndex + 1}/{numberOfBatches} from the Staging Table with {dbDataToBeProcessed.Count} records.");
                var apiDataToBeProcessed = ConvertToNonFaInvoiceDetails(dbDataToBeProcessed, outletLegacyErpDict, distributorLegacyErpDict, outletLegacyUserERPDict);

                var dataWithoutFieldUser = new HashSet<string>(apiDataToBeProcessed.Where(d => string.IsNullOrWhiteSpace(d.FieldUserErpId)).Select(d => d.InvoiceNumber));
                var dataWithoutRetailerErp = new HashSet<string>(apiDataToBeProcessed.Where(d => string.IsNullOrWhiteSpace(d.RetailerErpId)).Select(d => d.InvoiceNumber));

                if (dataWithoutFieldUser.Count > 0 || dataWithoutRetailerErp.Count > 0)
                {
                    if (dataWithoutFieldUser.Count > 0)
                    {
                        fileLogger.WriteLine($"Could not map a User to {dataWithoutFieldUser.Count} invoices:\n{System.Text.Json.JsonSerializer.Serialize(dataWithoutFieldUser)}", truncateConsoleMessage: true);
                    }
                    if (dataWithoutRetailerErp.Count > 0)
                    {
                        fileLogger.WriteLine($"Could not map a RetailerErpId to {dataWithoutRetailerErp.Count} invoices:\n{System.Text.Json.JsonSerializer.Serialize(dataWithoutRetailerErp)}", truncateConsoleMessage: true);
                    }
                    foreach (var dbData in dbDataToBeProcessed)
                    {
                        var noRetailer = dataWithoutRetailerErp.Contains(dbData.invoice_no);
                        if (dataWithoutFieldUser.Contains(dbData.invoice_no))
                        {
                            dbData.FAProcessAttempts++;
                            dbData.FAProcessFailures += " #*# Could not map a User to the invoice" + (noRetailer ? " and Could not map a RetailerErpId to the invoice" : "");
                            dbData.FAProcessFailures = dbData.FAProcessFailures.Length > ADLSInvoice.MaxLength ? dbData.FAProcessFailures.Substring(0, ADLSInvoice.MaxLength) : dbData.FAProcessFailures;
                            dbData.updatedAt = DateTime.UtcNow;
                        }
                        else if (noRetailer)
                        {
                            dbData.FAProcessAttempts++;
                            dbData.FAProcessFailures += " #*# Could not map a RetailerErpId to the invoice";
                            dbData.FAProcessFailures = dbData.FAProcessFailures.Length > ADLSInvoice.MaxLength ? dbData.FAProcessFailures.Substring(0, ADLSInvoice.MaxLength) : dbData.FAProcessFailures;
                            dbData.updatedAt = DateTime.UtcNow;
                        }
                    }
                }

                var totalInvoicesInBatch = apiDataToBeProcessed.Count;
                apiDataToBeProcessed = apiDataToBeProcessed
                    .Where(d => !dataWithoutFieldUser.Contains(d.InvoiceNumber) 
                    && !dataWithoutRetailerErp.Contains(d.InvoiceNumber)).ToList();

                var postTasks = new List<Task<int>>();

                for (int i = 0; i < apiDataToBeProcessed.Count; i += _batchSizeForExtAPI)
                {
                    var batch = apiDataToBeProcessed.Skip(i).Take(_batchSizeForExtAPI).ToList();
                    var postTask = SendPostRequestWithRetry(apiEndPoint, dbDataToBeProcessed, batch);
                    postTasks.Add(postTask);
                }

                await Task.WhenAll(postTasks);

                var dataToBeDeleted = dbDataToBeProcessed.Where(d => d.IsProcessed).Select(d => d.invoice_no).Distinct().ToList();
                var dataToBeUpdated = dbDataToBeProcessed.Where(d => !d.IsProcessed);

                await _sqlQueryHelper.retryPolicy.ExecuteAsync(async () =>
                {
                    fileLogger.WriteLine($"Deleting {dataToBeDeleted.Count()} invoices");
                    foreach (var batch in dataToBeDeleted.GetBatches(deleteBatchSize))
                    {
                        await _writableHccbDb.ADLSInvoice.Where(d => d.companyid == companyId && batch.Contains(d.invoice_no)).ExecuteDeleteAsync();
                    }

                    fileLogger.WriteLine($"Updating {dataToBeUpdated.Count()} invoices");
                    await _writableHccbDb.Database.CreateExecutionStrategy().Execute(async () =>
                    {
                        using var transaction = _writableHccbDb.Database.BeginTransaction();
                        await _writableHccbDb.BulkUpdateAsync(dataToBeUpdated, new BulkConfig
                        {
                            BatchSize = 5000,
                            UseTempDB = true,
                            PropertiesToIncludeOnUpdate = new List<string> { "IsProcessed", "FAProcessAttempts", "FAProcessFailures", "updatedAt" }
                        });
                        transaction.Commit();
                    });
                });

                var successFulRecords = postTasks.Sum(t => t.Result);
                responses.Add((successFulRecords, totalInvoicesInBatch - successFulRecords));

                // Clear the lists to free up memory
                dbDataToBeProcessed.Clear();
                postTasks.Clear();

                // Clear Entity Framework change tracker to release tracked entities
                _writableHccbDb.ChangeTracker.Clear();

                GC.Collect(generation: 2, GCCollectionMode.Aggressive); // Force garbage collection to free up memory
            }
            
            return (responses.Sum(r => r.successCount), responses.Sum(r => r.failureCount));
        }

        private async Task<int> SendPostRequestWithRetry(string apiEndPoint, List<ADLSInvoice> dbDataToBeProcessed, List<NonFaInvoiceDetailsModel> batch)
        {
            await semaphore.WaitAsync(); // Acquire semaphore slot
            try
            {
                var inputErpIdsJson = System.Text.Json.JsonSerializer.Serialize(batch.Select(e => e.InvoiceNumber));
                var hccbIntegrationLogs = new List<HCCBIntegrationAPILog>
                {
                    new HCCBIntegrationAPILog
                    {
                        CompanyId = companyId,
                        EntityEnum = EntityEnum.Invoice,
                        APIName = apiEndPoint,
                        HCCBIntegrationLogID = integrationLogId,
                        InputErpIds = inputErpIdsJson,
                        ApiType = Core.Models.HccbDbModels.ApiType.NsExtApi
                    } };
                var responseContent = await _httpRequestHelper.GetPostApiResponse(externalHttpClient, apiEndPoint, batch, doNotThrowError: true,
                    allowedFailureStatusCodes: new List<System.Net.HttpStatusCode> { System.Net.HttpStatusCode.BadRequest }, getFailedResponseString: true, hCCBIntegrationAPILogs: hccbIntegrationLogs);

                if (!responseContent.isSuccess)
                {
                    var failedInvoices = new HashSet<string>(batch.Select(d => d.InvoiceNumber));
                    fileLogger.WriteLine($"Failed to Post Invoices: {JsonConvert.SerializeObject(failedInvoices)}\nResponse: {responseContent.responseString}", truncateConsoleMessage: true);
                    foreach (var item in dbDataToBeProcessed)
                    {
                        if (failedInvoices.Contains(item.invoice_no))
                        {
                            item.FAProcessAttempts++;
                            item.updatedAt = DateTime.UtcNow;
                            item.FAProcessFailures += " #*# " + (responseContent.responseString.Length > 200 ? responseContent.responseString.Substring(0,200) : responseContent.responseString);
                            item.FAProcessFailures = item.FAProcessFailures.Length > ADLSInvoice.MaxLength ? item.FAProcessFailures.Substring(0, ADLSInvoice.MaxLength) : item.FAProcessFailures;
                        }
                    }
                    return 0;
                }
                var modelResponse = JsonConvert.DeserializeObject<ApiResponseV2<InvoiceAttributeEntities>>(responseContent.responseString);
#pragma warning disable CS8602 // Dereference of a possibly null reference for modelResponse.
                var invoiceResponseDict = modelResponse.ResponseList.ToLookup(r => r.Entities.InvoiceNumber);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

                foreach (var item in dbDataToBeProcessed)
                {
                    var response = invoiceResponseDict.Contains(item.invoice_no) ? invoiceResponseDict[item.invoice_no].FirstOrDefault() : null;
                    if (response?.Success == true)
                    {
                        item.IsProcessed = true;
                    }
                    else
                    {
                        item.FAProcessFailures += " #*# " + (string.IsNullOrWhiteSpace(response?.Message) ?"No Message Found In API" : response?.Message);
                        item.FAProcessFailures = item.FAProcessFailures.Length > ADLSInvoice.MaxLength ? item.FAProcessFailures.Substring(0, ADLSInvoice.MaxLength) : item.FAProcessFailures;
                    }
                    item.FAProcessAttempts++;
                    item.updatedAt = DateTime.UtcNow;
                }

                return modelResponse.ResponseStatusCount.Updated;
            }
            catch(Exception ex)
            {
                fileLogger.WriteLine($"Failed to Post Invoices: {JsonConvert.SerializeObject(batch.Select(i => i.InvoiceNumber))}\nException: {ex}");
                return 0;
            }
            finally
            {
                semaphore.Release(); // Release semaphore slot
            }
        }

        private bool CanDeserializeResponse(string responseContent, out ApiResponse? modelResponse)
        {
            try
            {
                modelResponse = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to Deserialize string into ApiResponse.\nError:\n{ex}\nString:\n{responseContent}");
                modelResponse = null;
                return false;
            }
        }

        private List<NonFaInvoiceDetailsModel> ConvertToNonFaInvoiceDetails(List<ADLSInvoice> invoicesData, Dictionary<string, string> outletLegacyErpDict, Dictionary<string, string> distributorLegacyErpDict, Dictionary<string, string> outletLegacyUserERPDict)
        {
            return invoicesData.GroupBy(p=> new { p.invoice_no,p.distributor_legacy_code, p.outlet_legacy_code, p.material, p.invoice_line_no, p.totalqty_ea }).Select(grp => grp.First())
                    .GroupBy(p => p.invoice_no)
                    .Select(grp => new NonFaInvoiceDetailsModel
                    {
                        InvoiceNumber = grp.Key,
                        OrderType = OrderType.Physical, // TODO: to confirm if the column is order_type in next iterartion
                        ReferenceNumber = !string.IsNullOrEmpty(grp.First().orderno) ? grp.First().orderno : null,
                        DistributorErpId = distributorLegacyErpDict.ContainsKey(grp.First().distributor_legacy_code) ? distributorLegacyErpDict[grp.First().distributor_legacy_code] : null,
                        InvoiceDate = (grp.First().invoice_date + grp.First().invoice_time),
                        RetailerErpId = outletLegacyErpDict.ContainsKey(grp.First().outlet_legacy_code)? outletLegacyErpDict[grp.First().outlet_legacy_code] : null,
                        InvoiceSkuDetails = grp.GroupBy(item => item.material)
                                    .Select(itemGroup => new InvoiceSkuDetails
                                    {
                                        ProductErpId = itemGroup.Key,
                                        Quantity = itemGroup.Sum(x => (double?)x.totalqty_ea) ?? 0,
                                        GrossRevenueValue = (double?)CalculateGrossRevenueValue(itemGroup)
                                    })
                                    .ToList(),
                        DeleteOlderInvoiceNumber = true,
                        FieldUserErpId = outletLegacyUserERPDict.ContainsKey(grp.First().outlet_legacy_code) ? outletLegacyUserERPDict[grp.First().outlet_legacy_code] : null,
                        IsSFAOrder = false,
                        ExternalOrderNumber = !string.IsNullOrEmpty(grp.First().orderno) ? grp.First().orderno : grp.Key,
                    }).ToList();
        }

        private static double CalculateGrossRevenueValue(IGrouping<string, ADLSInvoice> itemGroup)
        {
            var lineAmount = itemGroup.Sum(x => x.line_amount);
            var cgstAmount = itemGroup.Sum(x => x.line_amount * (x.ind_cgst_tax_rate / 100));
            var sgstAmount = itemGroup.Sum(x => x.line_amount * (x.ind_sgst_tax_rate / 100));
            var igstAmount = itemGroup.Sum(x => x.line_amount * (x.ind_igst_tax_rate / 100));
            var ugstAmount = itemGroup.Sum(x => x.line_amount * (x.ind_utgst_tax_rate / 100));
            var cessAmount = itemGroup.Sum(x => x.line_amount * (x.ind_cess_tax_rate / 100));

            return lineAmount + cgstAmount + sgstAmount + igstAmount + ugstAmount + cessAmount;
        }

        private async Task<DataLakeServiceClient> GetAdlsClient(bool fromSas = false, string accountName = null)
        {
            string uri = $"https://{accountName}.dfs.core.windows.net";
            if (fromSas)
            {
                uri = uri + $"?{AdlsSASToken.SASToken}";
                return new DataLakeServiceClient(new Uri(uri));
            }
            else
            {
                AccessTokenCredential tokenCredential = new AccessTokenCredential(await GetAzureADToken(accountName));
                return new DataLakeServiceClient(new Uri(uri), credential: tokenCredential);
            }
        }

        private async Task<string> GetAzureADToken(string accountName)
        {
            var adConfig = new AzureADConfig(accountName);

            var confidentialClientApplication = ConfidentialClientApplicationBuilder
            .Create(adConfig.ClientId)
            .WithClientSecret(adConfig.ClientSecret)
            .WithAuthority(new Uri($"https://login.microsoftonline.com/{adConfig.TenantId}"))
            .Build();

            var authResult = await confidentialClientApplication.AcquireTokenForClient(new[] { "https://storage.azure.com/.default" }).ExecuteAsync();
            return authResult.AccessToken;
        }
        protected override void UpdateLogs(List<IntegrationLog> logs)
        {
            int index = 0;
            foreach (var log in logs)
            {
                if (_summary?.Count > index)
                {
                    log.TotalReceived = _summary[index].TotalReceived;
                    log.TotalSuccess = _summary[index].Successful + _summary[index].Ignored;
                    log.TotalFailure = _summary[index].Failed;
                    log.LastUpdatedAt = DateTime.UtcNow;
                    log.FileName = queueRequest.FileName;
                    ++index;
                }
                else
                {
                    return;
                }
            }
        }

    }
}