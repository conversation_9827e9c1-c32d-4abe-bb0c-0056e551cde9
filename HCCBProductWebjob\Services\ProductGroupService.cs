﻿using Core.APIHelpers;
using Core.Loggers;
using Core.Repositories;
using HCCB.Core.Helpers;
using HCCBProductWebjob.Models;
using HCCBProductWebjob.Services.Interfaces;
using Library.ResponseHelpers;
using Library.StringHelpers;
using System.Net;

namespace HCCBProductWebjob.Services
{
    public class ProductGroupService : IProductGroupService
    {
        private readonly IProductGroupsRepository _productGroupsRepository;
        private readonly HttpRequestHelper _httpRequestHelper;
        private readonly FileLogger _logger;
        private readonly AppConfigSettings _appConfigSettings;
        private readonly IHttpClientFactory _httpClientFactory;

        public ProductGroupService(
            IProductGroupsRepository productGroupsRepository,
            HttpRequestHelper httpRequestHelper,
            FileLogger logger,
            AppConfigSettings appConfigSettings,
            IHttpClientFactory httpClientFactory)
        {
            _productGroupsRepository = productGroupsRepository;
            _httpRequestHelper = httpRequestHelper;
            _logger = logger;
            _appConfigSettings = appConfigSettings;
            _httpClientFactory = httpClientFactory;
        }

        public async Task EnsureProductGroupsExist(IEnumerable<string> eanCodes, long companyId)
        {
            var distinctCodes = eanCodes
                .Where(e => !string.IsNullOrWhiteSpace(e))
                .Distinct()
                .ToList();

            if (!distinctCodes.Any())
                return;

            var existingGroups = await _productGroupsRepository.GetProductGroupsDictionary(companyId, distinctCodes);
            var missingCodes = distinctCodes
                .Where(code => !existingGroups.ContainsKey(code.NormalizeCaps()))
                .ToList();

            if (!missingCodes.Any())
                return;

            _logger.WriteLine($"Creating {missingCodes.Count} new Product Groups...");

            string apiBasePath = _appConfigSettings.ExtApiBasePath;
            string apiUrl = $"{apiBasePath}/api/V3/Product/CreateUpdateProductGroup";
            string authorizationHeader = $"Basic {_appConfigSettings.HCCBClientAPIToken}";

            var httpClient = _httpClientFactory.CreateClient("FAExternalApiClient");

            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

            // Batch API calls (100 at a time)
            foreach (var batch in missingCodes.Chunk(100))
            {
                var inputs = batch.Select((code, idx) => new ProductGroupInput
                {
                    Name = code,
                    ERPId = code,
                    Sequence = 13,
                    Description = $"Item Master Integration"
                }).ToList();

                var apiResponse = await _httpRequestHelper.GetParsedResponse<ApiResponseV2<ProductGroupAttributes>>(
                        httpClient, apiUrl, inputs,
                        allowedFailureStatusCodes: new List<HttpStatusCode> { HttpStatusCode.BadRequest },
                        doNotThrowError: true
                    );

                if (!apiResponse.IsSuccess())
                {
                    _logger.WriteLine($"Failed creating product groups: {apiResponse.Message}");
                }
            }
        }
    }
}