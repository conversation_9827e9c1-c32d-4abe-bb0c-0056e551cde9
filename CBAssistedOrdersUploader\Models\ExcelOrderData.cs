namespace CBAssistedOrdersUploader.Models
{
    public class ExcelOrderData
    {
        public long S_No { get; set; }
        public string? SALES_POINT_ID { get; set; }
        public string? STL_ID { get; set; }
        public string? STL_NAME { get; set; }
        public string? MARKET_AREA_NAME { get; set; }
        public string? DISTRIBUTOR_ID { get; set; }
        public string? OUTLET_ID { get; set; }
        public string? OUTLET_NAME { get; set; }
        public string? ORDER_TYPE { get; set; }
        public long? ORDER_ID_IN_CHATBOT { get; set; }
        public string? ORDER_DATE { get; set; }
        public string? ORDER_TIME { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public int? Ko_Week { get; set; }
        public int? Ko_Month { get; set; }
        public string? BUSINESS_UNIT { get; set; } // Will be ignored
        public string? DEVICE_ID { get; set; } // Will be ignored
    }
}
