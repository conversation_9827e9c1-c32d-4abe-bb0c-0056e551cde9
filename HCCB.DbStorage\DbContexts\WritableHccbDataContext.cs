using Core.Models.HccbDbModels;
using HCCB.DbStorage.DbModel.HccbDbModel;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.DbContexts
{
    public class WritableHccbDataContext : DbContext
    {
        public WritableHccbDataContext(DbContextOptions<WritableHccbDataContext> options)
            : base(options)
        {
        }

        public DbSet<IntegrationLastSyncTime> IntegrationLastSyncTimes { get; set; }
        public DbSet<HccbOrder> HccbOrders { get; set; }
        public DbSet<OrderStatusDump> OrderStatusDumps { get; set; }
        public DbSet<IntegrationLog> HCCBIntegrationLogs { get; set; }
        public DbSet<HCCBIntegrationAPILog> HCCBIntegrationAPILogs { get; set; }
        public DbSet<OrderSyncFallbackProcessorAttendanceId> OrderSyncFallbackProcessorAttendanceIds { get; set; }
        public DbSet<OrdersApiLog> OrdersApiLogs { get; set; }
        public DbSet<VisitsApiLog> VisitsApiLogs { get; set; }
        public DbSet<HccbTaskAchievementAndReward> HccbTaskAchievementAndRewards { get; set; }
        public DbSet<TaskSummary> TaskSummary { get; set; }
        public DbSet<TaskSummaryDetail> TaskSummaryDetails { get; set; }
        public DbSet<MaxerienceLog> MaxerienceLog { get; set; }
        public DbSet<Scenes> Scenes { get; set; }
        public DbSet<KPIIncentive> KPIIncentives { get; set; }
        public DbSet<WeeklyRouteUpload> WeeklyRouteUploads { get; set; }
        public DbSet<BackdatedWeeklyOff> BackdatedWeeklyOffs { get; set; }
        public DbSet<HccbExportRequest> HccbExportRequests { get; set; }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
