# CB Assisted Orders Uploader

A WPF application for uploading order data from Excel files to SQL Server databases using bulk insert operations.

## Features

- **Excel File Processing**: Reads .xlsx files with order data
- **Data Transformation**: Converts Excel data to database format with proper date/time parsing
- **Bulk Insert**: Uses EF Core BulkExtensions for optimized database operations
- **Progress Tracking**: Real-time progress updates during upload process
- **Error Handling**: Comprehensive error logging and user feedback
- **Connection Testing**: Validates database connection and table existence

## Requirements

- .NET 9.0
- SQL Server database
- Excel files in .xlsx format

## Usage

1. **Connection String**: Enter your SQL Server connection string
2. **Table Name**: Specify the target table name
3. **Excel File**: Select the .xlsx file containing order data
4. **Test Connection**: Verify database connectivity and table existence
5. **Upload**: Start the bulk insert process

## Excel File Format

The application expects Excel files with the following columns:

| Column Name | Description |
|-------------|-------------|
| S_NO | Serial number |
| SALES_POINT_ID | Sales point identifier |
| STL_ID | STL identifier |
| STL_NAME | STL name (maps to STL_ME) |
| MARKET_AREA_NAME | Market area name (maps to MARKET_AREA_ME) |
| DISTRIBUTOR_ID | Distributor identifier |
| OUTLET_ID | Outlet identifier |
| OUTLET_NAME | Outlet name (maps to OUTLET_ME) |
| ORDER_TYPE | Type of order |
| ORDER_ID_IN_CHATBOT | Chatbot order ID |
| ORDER_DATE | Order date (DD-MM-YYYY format) |
| ORDER_TIME | Order time |
| LATITUDE | Latitude coordinate |
| LONGITUDE | Longitude coordinate |
| KO_WEEK | KO week number |
| KO_MONTH | KO month number |
| BUSINESS_UNIT | Business unit (ignored) |
| DEVICE_ID | Device ID (ignored) |

## Database Schema

The target table should have the following structure:

```sql
CREATE TABLE [YourTableName] (
    [S_No] bigint NOT NULL,
    [SALES_POINT_ID] nvarchar(100) NULL,
    [FA_User_Id] bigint NULL,
    [STL_ID] nvarchar(100) NULL,
    [STL_ME] nvarchar(100) NULL,
    [MARKET_AREA_ME] nvarchar(100) NULL,
    [DISTRIBUTOR_ID] nvarchar(100) NULL,
    [OUTLET_ID] nvarchar(100) NULL,
    [OUTLET_ME] nvarchar(max) NULL,
    [ORDER_TYPE] nvarchar(100) NULL,
    [ORDER_ID_IN_CHATBOT] bigint NULL,
    [ORDER_DATE] datetime2(7) NULL,
    [ORDER_TIME] datetime2(7) NULL,
    [OrderDate_Time] datetime2(7) NULL,
    [Latitude] float NULL,
    [Longitude] float NULL,
    [Ko_Week] int NULL,
    [Ko_Month] int NULL,
    [IsOutletGeolocation] bit NOT NULL,
    [CreatedAt] datetime2(7) NOT NULL,
    PRIMARY KEY ([S_No])
);
```

## Data Processing

- **NA and Blank Values**: Treated as NULL
- **Date Parsing**: ORDER_DATE parsed as DD-MM-YYYY format
- **DateTime Combination**: ORDER_DATE and ORDER_TIME combined into OrderDate_Time
- **Fixed Values**: FA_User_Id set to NULL, IsOutletGeolocation set to 0
- **CreatedAt**: Set to process start time in UTC

## Performance Features

- Optimized batch size (10,000 records per batch)
- Uses temporary database for better performance
- Single transaction for all data
- Progress reporting during upload

## Error Handling

- Detailed error logs saved to desktop
- User-friendly error messages
- Connection validation before upload
- Cancellation support during upload
