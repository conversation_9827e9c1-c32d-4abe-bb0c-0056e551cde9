﻿using EntityHelper;

namespace Core.Models.HccbDbModels
{
    public class MaxerienceLog : ICreatedEntity, IUpdatableEntity
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long UserId { get; set; }

        public long OutletId { get; set; }

        public string EventId { get; set; }

        public string MaxerienceSessionId { get; set; }

        public DateTime MaxerienceSessionStartTime { get; set; }

        public DateTime MaxerienceSessionCloseTime { get; set; }

        public int TotalScenes { get; set; }

        public int TotalImages { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public bool IsSyncedToHccb { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public long QualifiedDate { get; set; }
    }

}