﻿{
  "ConnectionStrings": {
    "NSDataApiConnectionString": "BaseUrl=https://fa-reportapi.fieldassist.io/;AuthToken=56SbkbmV+#?p+dSNgGPNz8;",
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=hccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "WritableTransactionDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Database= FA_Transactions;User ID=Azure_ReadWrite;Password=****************;Trusted_Connection=False;Encrypt=True;Connection Timeout=30;Max Pool Size=1000;",
    "ReadonlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;",
    "WritableHccbDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;"
  }
}