# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["HccbExportsProcessor/HccbExportProcessor.csproj", "HccbExportsProcessor/"]
COPY ["Core/Core.csproj", "Core/"]
COPY ["FA_Libraries/EntityHelper/EntityHelper.csproj", "FA_Libraries/EntityHelper/"]
COPY ["FA_Libraries/Libraries.CommonEnums/Libraries.CommonEnums.csproj", "FA_Libraries/Libraries.CommonEnums/"]
COPY ["FA_Libraries/Library.CommonHelpers/Library.CommonHelpers.csproj", "FA_Libraries/Library.CommonHelpers/"]
COPY ["FA_Libraries/fa_dotnet_core/FA.Cache/FA.Cache.csproj", "FA_Libraries/fa_dotnet_core/FA.Cache/"]
COPY ["FA_Libraries/fa_dotnet_logger/FA.Logger/FA.Logger.csproj", "FA_Libraries/fa_dotnet_logger/FA.Logger/"]
COPY ["FA_Libraries/Libraries.CommonModels/Libraries.CommonModels.csproj", "FA_Libraries/Libraries.CommonModels/"]
COPY ["FA_Libraries/Library.DateTimeHelpers/Library.DateTimeHelpers.csproj", "FA_Libraries/Library.DateTimeHelpers/"]
COPY ["FA_Libraries/Library.EmailService/Library.EmailService.csproj", "FA_Libraries/Library.EmailService/"]
COPY ["FA_Libraries/Library.Infrastructure/Library.Infrastructure.csproj", "FA_Libraries/Library.Infrastructure/"]
COPY ["FA_Libraries/Library.ResilientHttpClient/Library.ResilientHttpClient.csproj", "FA_Libraries/Library.ResilientHttpClient/"]
COPY ["FA_Libraries/Library.SlackService/Library.SlackService.csproj", "FA_Libraries/Library.SlackService/"]
COPY ["FA_Libraries/Library.StorageWriter/Library.StorageWriter.csproj", "FA_Libraries/Library.StorageWriter/"]
COPY ["FA_Libraries/Libraries.Cryptography/Libraries.Cryptography.csproj", "FA_Libraries/Libraries.Cryptography/"]
COPY ["HCCB.DbStorage/HCCB.DbStorage.csproj", "HCCB.DbStorage/"]
COPY ["FA_Libraries/Library.SqlHelper/Library.SqlHelper.csproj", "FA_Libraries/Library.SqlHelper/"]
COPY ["FA_Libraries/Library.StringHelpers/Library.StringHelpers.csproj", "FA_Libraries/Library.StringHelpers/"]
COPY ["HCCB-ADLS.DbStorage/HCCB_ADLS.DbStorage.csproj", "HCCB-ADLS.DbStorage/"]
COPY ["FA_Libraries/Libraries.Authentication/AuditHelper.csproj", "FA_Libraries/Libraries.Authentication/"]
COPY ["HCCB_ADLS.Core/HCCB_ADLS.Core.csproj", "HCCB_ADLS.Core/"]
COPY ["Hccb.Helpers/HCCB.Core.csproj", "Hccb.Helpers/"]
COPY ["FA_Libraries/Library.ResponseHelpers/Library.ResponseHelpers.csproj", "FA_Libraries/Library.ResponseHelpers/"]
RUN dotnet restore "./HccbExportsProcessor/HccbExportProcessor.csproj"
COPY . .
WORKDIR "/src/HccbExportsProcessor"
RUN dotnet build "./HccbExportProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./HccbExportProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "HccbExportProcessor.dll"]