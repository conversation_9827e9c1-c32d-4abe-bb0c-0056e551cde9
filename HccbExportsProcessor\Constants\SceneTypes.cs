using System.Collections.Generic;

namespace HccbExportsProcessor.Constants
{
    public static class SceneTypes
    {
        public static readonly IReadOnlyDictionary<int, string> Types = new Dictionary<int, string>
        {
            [1] = "Cooler",
            [2] = "Ambient",
            [3] = "Promotional"
        };

        public static readonly IReadOnlyDictionary<int, string> CoolerSubTypes = new Dictionary<int, string>
        {
            [1] = "KO Cooler [Mandatory]",
            [2] = "Competitor",
            [3] = "Customer",
            [0] = "No Image - Cooler Not Available / Took Order Away From Outlet"
        };

        public static readonly IReadOnlyDictionary<int, string> PromotionalSubTypes = new Dictionary<int, string>
        {
            [21] = "FootFall (Population) Driver - DPS/Flange/Standee/OnewayVision/Rack/Crate/Cooler with Price Point communication",
            [20] = "Transaction (Impulse) Driver - CounterTop/Hanger/Visi Top/Shelf/Food Association board/Menu/Price Communication Warm",
            [0] = "No Image - Took Order Away From Outlet"
        };

        public static readonly IReadOnlyDictionary<int, string> AmbientSubTypes = new Dictionary<int, string>
        {
            [6] = "Counter Top Display",
            [5] = "Floor",
            [4] = "Rack Display",
            [0] = "No Image - Took Order Away From Outlet"
        };
    }
}
