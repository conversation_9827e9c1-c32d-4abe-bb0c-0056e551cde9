using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HCCB.DbStorage.DbModel.HccbDbModel
{
    [Table("HCCBExportRequests")]
    public class HccbExportRequest
    {
        [Key]
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public int ExportType { get; set; }

        public long RequestedById { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? ExecutionStartedAt { get; set; }

        public DateTime? ExecutionCompletedAt { get; set; }

        public ExportStatus Status { get; set; }

        public string? OutputLink { get; set; }

        public string? StatusMessage { get; set; }

        public string? Parameters { get; set; }
    }

    public enum ExportStatus
    {
        Pending = 0,
        Processing = 1,
        Completed = 2,
        Failed = 3
    }
}
