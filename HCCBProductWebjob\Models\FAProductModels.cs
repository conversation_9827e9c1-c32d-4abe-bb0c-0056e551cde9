using Library.ResponseHelpers;
namespace HCCBProductWebjob.Models
{
    public class FACreateProduct
    {
        public string ProductDivision { get; set; }
        public string Brand { get; set; }
        public string Category { get; set; }
        public string DisplayCategory { get; set; }
        public string StandardUnit { get; set; }
        public string SuperUnit { get; set; }
        public string Name { get; set; }
        public string VariantName { get; set; }
        public string Display_MRP { get; set; }
        public decimal? MRP { get; set; }
        public double? Price { get; set; }
        public string Unit { get; set; }
        public string LocalName { get; set; }
        public string Image { get; set; }
        public double? StandardUnitConversionFactor { get; set; }
        public double? SuperUnitConversionFactor { get; set; }
        public int? DisplayOrder { get; set; }
        public string PackagingType { get; set; }
        public string Channel { get; set; }
        public bool? IsPromoted { get; set; }
        public string Description { get; set; }
        public string ProductERPId { get; set; }
        public string Schemes { get; set; }
        public string PackSize { get; set; }
        public string Color { get; set; }
        public string AlternateCategory { get; set; }
        public bool? IsNewLaunch { get; set; }
        public int? MBQ { get; set; }
        public int? ExpiryInDays { get; set; }
        public string GSTCategory { get; set; }
        public string ShortName { get; set; }
        public int? HSNCode { get; set; }
        public string HSNCodeStr { get; set; }
        public int? ShelfLifeInDays { get; set; }
        public string Barcode { get; set; }
        public decimal? NetWeight { get; set; }
        public decimal? GrossWeight { get; set; }
        public decimal? PTRMT { get; set; }
        public decimal? PTDSuper { get; set; }
        public decimal? PTDSub { get; set; }
        public decimal? PTD { get; set; }
        public string AttributeText1 { get; set; }
        public string AttributeText2 { get; set; }
        public bool? AttributeBoolean1 { get; set; }
        public bool? AttributeBoolean2 { get; set; }
        public double? AttributeNumber1 { get; set; }
        public double? AttributeNumber2 { get; set; }
        public DateTime? AttributeDate1 { get; set; }
        public DateTime? AttributeDate2 { get; set; }
        public double? PTRDelta { get; set; }
        public long? EpochTime { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public string AdditionalUnit { get; set; }
        public double? AdditionalUnitConversionFactor { get; set; }
        public string P1 { get; set; }
        public string P2 { get; set; }
        public string P3 { get; set; }
        public string P4 { get; set; }
        public string P5 { get; set; }
        public string Category2 { get; set; }
        public string ProductGroupCode { get; set; }
    }
    #region GettingProductDetails
    public class ProductsErpInput
    {
        public List<string> ProductErpIds { get; set; }
    }
    public class ProductsMin
    {
        public string Name { get; set; }
        public string ErpId { get; set; }
    }
    #endregion
    #region UpdateProducts
    public class UpdateProduct
    {
        public bool? Deactivated { get; set; }
        public string ProductGuid { get; set; }
        public string Name { get; set; }
        public string VariantName { get; set; }
        public string Display_MRP { get; set; }
        public double? MRP { get; set; }
        public double? Price { get; set; }
        public string Unit { get; set; }
        public string LocalName { get; set; }
        public string Image { get; set; }
        public double? StandardUnitConversionFactor { get; set; }
        public double? SuperUnitConversionFactor { get; set; }
        public int? DisplayOrder { get; set; }
        public string PackagingType { get; set; }
        public string Channel { get; set; }
        public bool? IsPromoted { get; set; }
        public string Description { get; set; }
        public string ProductERPId { get; set; }
        public string Schemes { get; set; }
        public string PackSize { get; set; }
        public string Color { get; set; }
        public string AlternateCategory { get; set; }
        public bool? IsNewLaunch { get; set; }
        public int? MBQ { get; set; }
        public int? ExpiryInDays { get; set; }
        public string GSTCategory { get; set; }
        public string ShortName { get; set; }
        public int? HSNCode { get; set; }
        public string HSNCodeStr { get; set; }
        public int? ShelfLifeInDays { get; set; }
        public string Barcode { get; set; }
        public double? NetWeight { get; set; }
        public double? GrossWeight { get; set; }
        public double? PTRMT { get; set; }
        public double? PTDSuper { get; set; }
        public double? PTDSub { get; set; }
        public double? PTD { get; set; }
        public string AttributeText1 { get; set; }
        public string AttributeText2 { get; set; }
        public bool? AttributeBoolean1 { get; set; }
        public bool? AttributeBoolean2 { get; set; }
        public double? AttributeNumber1 { get; set; }
        public double? AttributeNumber2 { get; set; }
        public DateTime? AttributeDate1 { get; set; }
        public DateTime? AttributeDate2 { get; set; }
        public double? PTRDelta { get; set; }
        public int? EpochTime { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public string AdditionalUnit { get; set; }
        public double? AdditionalUnitConversionFactor { get; set; }
        public string P1 { get; set; }
        public string P2 { get; set; }
        public string P3 { get; set; }
        public string P4 { get; set; }
        public string P5 { get; set; }
        public string Category2 { get; set; }
        public string ProductGroupCode { get; set; }
    }
    #endregion

    public class UpdatedProduct
    {
        public string ERPId { get; set; }
        public string GUID { get; set; }
        public string AlternateCategory { get; set; }
        public string Message { get; set; }
        public ResponseStatus ResponseStatus { get; set; }
    }

    public class ProductsHierarchy
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string ErpId { get; set; }
        public string AlternateCategoryName { get; set; }
        public string SecondaryCategoryName { get; set; }
        public string PrimaryCategoryName { get; set; }
        public string ProductDivisionName { get; set; }
        public bool IsDeactive { get; set; }
    }
}
