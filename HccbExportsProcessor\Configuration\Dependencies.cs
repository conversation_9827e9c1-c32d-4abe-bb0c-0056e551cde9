using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using HCCB.DbStorage.DbContexts;
using Core.Loggers;
using SlackNet;
using SlackNet.Extensions.DependencyInjection;
using HccbExportProcessor.Services;
using Core.Repositories;
using HCCB.DbStorage.Repositories.HccbRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Library.StorageWriter.Reader_Writer;
using Serilog.Events;
using Serilog;
using SlackNet.Events;

namespace HccbExportProcessor.Configuration
{
    public static class Dependencies
    {
        public const string ContainerForImages = "ir-images";
        public const string TestChannelId = "C08D4PSBW4V";

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            // Slack Configuration
            var slackApiToken = config["Slack:ApiToken"];
            var channelId = config["Slack:ChannelId"] ?? TestChannelId;

            // Database Contexts

            var hccbDbConnectionString = config.GetConnectionString("WritableHccbDbConnectionString");
            services.AddDbContext<WritableHccbDataContext>(options =>
                options.UseSqlServer(hccbDbConnectionString, SqlResiliencyBuilder));

            services.AddScoped(s => new HccbDbSqlDataReader(hccbDbConnectionString, 300));
            var readOnlyMasterDbConnectionString = config.GetConnectionString("ReadOnlyMasterDbConnectionString");

            services.AddScoped(e =>
                new MasterDbSqlDataReader(readOnlyMasterDbConnectionString, 600));

            services.AddDbContext<ReadOnlyMasterDbContext>(options =>
            {
                options.UseSqlServer(readOnlyMasterDbConnectionString, SqlResiliencyBuilder);
            });
            services.AddHttpClient();
            services.AddSlackNet(c => c.UseApiToken(slackApiToken));
            services.AddSingleton<ISlackLogHelper>(sp => new SlackLogHelperV2(sp.GetRequiredService<IHttpClientFactory>(),
                    sp.GetRequiredService<ISlackApiClient>(), masterStorageConnectionString: string.Empty, channelId));

            var hccbStorageConnectionString = config.GetConnectionString("HCCBStorageConnectionString");
            services.AddScoped(d => new HCCBBlobReader(hccbStorageConnectionString, ContainerForImages));
            services.AddScoped(d => new HCCBBlobWriter(hccbStorageConnectionString));

            // Services
            services.AddScoped<HccbExportService>();
            services.AddScoped<IPdfExportService, PdfExportService>();

            // Repositories
            services.AddScoped<IMaxerienceLogRepository, MaxerienceLogRepository>();
            services.AddScoped<IHccbExportRequestRepository, HccbExportRequestRepository>();
            services.AddScoped<IFaMasterRepository, FaMasterRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<ILocationsRepository, LocationsRepository>();
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder sqlOptions)
        {
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        }
        public static void LogSetUp(LoggerConfiguration loggerConfiguration, IConfiguration config)
        {
            loggerConfiguration
                .Enrich.FromLogContext()
                .WriteTo.Console(LogEventLevel.Error);
        }
    }
}
