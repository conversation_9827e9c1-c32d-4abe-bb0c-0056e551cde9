﻿using HCCB_ADLS_Triggered.Configurations;
using HCCB_ADLS_Triggered.Interfaces;

namespace HCCB_ADLS_Triggered.Services
{
    public class ADLSContextService : IADLSContextService
    {
        private readonly IADLSContextRepository _adlsMetricRepository;
        private readonly IQueueManager queueManager;
        public ADLSContextService(IADLSContextRepository adlsMetricRepository, IQueueManager queueManager)
        {
            _adlsMetricRepository = adlsMetricRepository;
            this.queueManager = queueManager;
        }

        public async Task ProcessADLSContext()
        {
            try
            {
                Console.WriteLine($"Starting trigger HCCB-ADLS-Metric Webjob!");
                Console.WriteLine("Starting trigger HCCB-ADLS-Metric Webjob!");
                var dbAdlsData = await _adlsMetricRepository.GetADLSContext(Dependencies.CompanyId);

                Console.WriteLine($"Adding data to Queue!");
                await queueManager.AddToHccbADLSQueue(dbAdlsData);
                Console.WriteLine("Ending trigger HCCB-ADLS-Metric Webjob!");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in trigger HCCB-ADLS-Metric Webjob: {ex}");
            }
        }

        public async Task ProcessADLSNonFAInvoiceContext()
        {
            try
            {
                Console.WriteLine($"Starting trigger HCCB-ADLS-NonFAInvoice Webjob!");
                var dbAdlsData = await _adlsMetricRepository.GetADLSInvoiceContext(Dependencies.CompanyId);
                Console.WriteLine($"Adding data to Queue!");
                await queueManager.AddToHccbADLSNonFaInvoiceQueue(dbAdlsData);
                Console.WriteLine("Ending trigger HCCB-ADLS-NonFAInvoice Webjob!");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in trigger HCCB-ADLS-NonFAInvoice Webjob: {ex}");
            }
        }

    }
}
