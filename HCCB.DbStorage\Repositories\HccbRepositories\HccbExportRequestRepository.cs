using System;
using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.DbModel.HccbDbModel;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.HccbRepositories
{
    public class HccbExportRequestRepository : IHccbExportRequestRepository
    {
        private readonly WritableHccbDataContext _dbContext;

        public HccbExportRequestRepository(WritableHccbDataContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<HccbExportRequest> GetByIdAsync(long id)
        {
            return await _dbContext.HccbExportRequests.FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task UpdateAsync(HccbExportRequest exportRequest)
        {
            _dbContext.HccbExportRequests.Update(exportRequest);
            await _dbContext.SaveChangesAsync();
        }
    }
}
