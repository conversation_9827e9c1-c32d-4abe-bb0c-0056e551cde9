using CBAssistedOrdersUploader.Models;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace CBAssistedOrdersUploader.Services
{
    public class DatabaseService
    {
        private readonly ILogger<DatabaseService> _logger;
        
        public DatabaseService(ILogger<DatabaseService> logger)
        {
            _logger = logger;
        }
        
        public async Task<int> BulkInsertOrdersAsync(
            string connectionString, 
            string tableName, 
            List<OrderData> orders, 
            IProgress<string> progress,
            CancellationToken cancellationToken = default)
        {
            progress?.Report("Starting bulk insert operation...");
            
            var options = new DbContextOptionsBuilder<OrderDbContext>()
                .UseSqlServer(connectionString)
                .Options;
            
            using var context = new OrderDbContext(options, tableName);
            
            // Configure bulk insert options for optimal performance
            var bulkConfig = new BulkConfig
            {
                BatchSize = 10000, // Optimized batch size
                BulkCopyTimeout = 0, // No timeout
                UseTempDB = true, // Use temp database for better performance
                WithHoldlock = false,
                SetOutputIdentity = false,
                TrackingEntities = false
            };
            
            var totalRecords = orders.Count;
            var insertedRecords = 0;
            
            try
            {
                // Process in batches to show progress
                for (int i = 0; i < orders.Count; i += bulkConfig.BatchSize)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var batch = orders.Skip(i).Take(bulkConfig.BatchSize).ToList();
                    
                    progress?.Report($"Inserting batch {(i / bulkConfig.BatchSize) + 1} ({batch.Count} records)...");
                    
                    await context.BulkInsertAsync(batch, bulkConfig, cancellationToken: cancellationToken);
                    
                    insertedRecords += batch.Count;
                    progress?.Report($"Inserted {insertedRecords} of {totalRecords} records...");
                }
                
                progress?.Report($"Successfully inserted all {insertedRecords} records.");
                return insertedRecords;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk insert operation");
                throw;
            }
        }
        
        public async Task<bool> TestConnectionAsync(string connectionString, string tableName)
        {
            try
            {
                var options = new DbContextOptionsBuilder<OrderDbContext>()
                    .UseSqlServer(connectionString)
                    .Options;
                
                using var context = new OrderDbContext(options, tableName);
                await context.Database.OpenConnectionAsync();
                
                // Check if table exists
                context.Orders.Any();

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
    
    public class OrderDbContext : DbContext
    {
        private readonly string _tableName;
        
        public OrderDbContext(DbContextOptions<OrderDbContext> options, string tableName) : base(options)
        {
            _tableName = tableName;
        }
        
        public DbSet<OrderData> Orders { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OrderData>(entity =>
            {
                entity.ToTable(_tableName);
                entity.HasKey(e => e.S_No);
                entity.Property(e => e.S_No).ValueGeneratedNever(); // Don't auto-generate, use from Excel
            });
        }
    }
}
