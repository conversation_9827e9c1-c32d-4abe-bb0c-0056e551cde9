using HccbExportProcessor.Services;
using Core.Models.QueueModel;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;
using System.Text.Json;
using Core.Loggers;
using HccbExportProcessor.Models;

namespace HccbExportProcessor
{
    public class HccbExportProcessor
    {
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly HccbExportService _hccbExportService;
        private const string HccbExportQueue = "hccb-exports-queue";

        public HccbExportProcessor(ISlackLogHelper slackLogHelper, HccbExportService hccbExportService)
        {
            _slackLogHelper = slackLogHelper;
            _hccbExportService = hccbExportService;
        }
        public async Task HccbExportsProcessor([QueueTrigger(HccbExportQueue)] HccbExportQueueData queueData)
        {
            var sw = new Stopwatch();
            sw.Start();
            try
            {
                Console.WriteLine($"----------START----------");
                Console.WriteLine($"Received Event...");
                Console.WriteLine(JsonSerializer.Serialize(queueData));

                await _hccbExportService.ProcessExportAsync(queueData);

                var timeTaken = sw.ElapsedMilliseconds;
                Console.WriteLine($"✅ Event processed successfully in {timeTaken} milliseconds");
            }
            catch (Exception ex)
            {
                await _slackLogHelper.SendCustomSlackMessage("Error processing Export", false, $"UnHandled Exception Occured! \n" +
                    $"Event: exportId: {queueData.ExportId}, exportType: {queueData.ExportType}, companyId: {queueData.CompanyId}",
                    exceptionMessage: ex.GetBaseException().Message);
            }
            finally
            {
                sw.Stop();
                Console.WriteLine($"----------END----------");
            }
        }
    }
}
