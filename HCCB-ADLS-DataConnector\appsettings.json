{"ASPNETCORE_ENVIRONMENT": "Development", "ConnectionStrings": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=betahccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;", "WritableHccbDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;", "MasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;"}, "SlackChannelId": "C08D4PSBW4V", "CompanyId": 193017}