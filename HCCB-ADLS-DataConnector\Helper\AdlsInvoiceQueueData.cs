﻿namespace HCCB_ADLS_DataConnector.Helper;

public class AdlsInvoiceQueueData
{
    public string DirectoryPath { get; set; }

    public string FileName { get; set; }

    public string AccountName { get; set; }
    public string FileSystemName { get; set; }

    public bool IsfromSAS { get; set; }

    public List<string> HeaderNames { get; set; }

    public List<Condition> RowConditions { get; set; }

    public string ExternalApiBaseAddress { get; set; }
    public string ExternalAPIUserName { get; set; }
    public string ExternalAPIPassword { get; set; }
    public string ExternalApiEndPoint { get; set; }
    public bool ConsiderAllHeaders { get; set; }
    public long CompanyId { get; set; }
    public bool ConsiderSpecifiedOutletLegacyCodes { get; set; } // Temperory column for adls-invoice processor 
    public List<string> OutletLegacyCodes { get; set; } // Temperory column for adls-invoice processor 
    public bool SkipFileRead { get; set; }
}

public class Condition
{
    public string ColumnName { get; set; }

    public string Value { get; set; }
}
