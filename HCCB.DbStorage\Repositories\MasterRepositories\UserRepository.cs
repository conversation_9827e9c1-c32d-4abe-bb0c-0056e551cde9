using HCCB.DbStorage.DbContexts;
using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class UserRepository : IUserRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;

        public UserRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
        }

        public async Task<Dictionary<long, EntityMinWithErp>> GetUserDictionary(List<long> userIds)
        {
            return await _readOnlyMasterDbContext.Employees
                .Where(e => userIds.Contains(e.Id))
                .ToDictionaryAsync(e => e.Id, e => new EntityMinWithErp { ErpId = e.ErpId, Name = e.Name });
        }
    }
}
