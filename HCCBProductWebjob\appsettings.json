{"ConnectionStrings": {"MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;", "HCCBFaDataConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;", "ReadOnlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;"}, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet", "APIBasePath": "https://fa-external-apis-release-testing.azurewebsites.net", "TelegramBotToken": "**********************************************", "HCCBClientAPIToken": "bWVoYWs6dFptZEFMWGpaJkZMV01mZzg3elM=", "ExtApiBasePath": "http://api-debug-enterprise-1.fieldassist.io"}, "Slack": {"BotToken": "******************************************************", "ChannelId": "C08D4PSBW4V"}, "hccbAPIhelper": {"Username": "2Rg5saS1uG5c1W5YJbBhex", "Password": "FpPk0WfJ0", "PartnerId": 1, "HccbBaseUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com", "IpcInterfaceId": 4, "ItemInterfaceId": 3}, "CompanyId": 193017}