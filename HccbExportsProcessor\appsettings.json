{"ConnectionStrings": {"WritableHccbDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Max Pool Size=100;", "ReadonlyMasterDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Database=F2KLocationsNetworkV3_HCCB;User ID=f2kadmin;Password=*************************;Trusted_Connection=False;Encrypt=True;Connection Timeout=30;Max Pool Size=50;", "HCCBStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=betahccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=betahccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "Slack": {"ApiToken": "******************************************************"}}