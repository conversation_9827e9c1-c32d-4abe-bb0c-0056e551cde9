﻿using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Services.AppAuthentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureKeyVault;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using HCCB_ADLS_DataConnector.Configuration;
using Serilog;

namespace HCCB_ADLS_DataConnector
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((config) =>
                {
                    var env = Environment.GetEnvironmentVariable("BuildEnvironment");
                    config.AddJsonFile($"appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddAzureStorageQueues(c =>
                    {
                        c.BatchSize = 1;
                        c.NewBatchThreshold = 0;
                        c.MaxPollingInterval = TimeSpan.FromSeconds(8);
                        c.VisibilityTimeout = TimeSpan.FromMinutes(15);
                        c.MaxDequeueCount = 2;
                    });
                })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseSerilog((_, loggerConfiguration) =>
    {
        Dependencies.LogSetUp(loggerConfiguration, _.Configuration);
    })
        .UseConsoleLifetime();
            var host = builder.Build();
            using (host)
            {
                Log.Information("Running Webjob");
                await host.RunAsync();
            }
        }
    }
}