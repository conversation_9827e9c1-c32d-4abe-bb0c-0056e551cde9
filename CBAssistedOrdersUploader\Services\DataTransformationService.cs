using CBAssistedOrdersUploader.Models;
using System.Globalization;

namespace CBAssistedOrdersUploader.Services
{
    public class DataTransformationService
    {
        public List<OrderData> TransformData(List<ExcelOrderData> excelData, DateTime processStartTime, IProgress<string> progress)
        {
            progress?.Report("Transforming data...");
            
            var transformedData = new List<OrderData>();
            
            for (int i = 0; i < excelData.Count; i++)
            {
                var excelOrder = excelData[i];
                
                var orderData = new OrderData
                {
                    S_No = excelOrder.S_No,
                    SALES_POINT_ID = excelOrder.SALES_POINT_ID,
                    FA_User_Id = null, // Always NULL as per requirements
                    STL_ID = excelOrder.STL_ID,
                    STL_ME = excelOrder.STL_NAME, // STL NAME maps to STL_ME
                    MARKET_AREA_ME = excelOrder.MARKET_AREA_NAME, // MARKET AREA NAME maps to MARKET_AREA_ME
                    DISTRIBUTOR_ID = excelOrder.DISTRIBUTOR_ID,
                    OUTLET_ID = excelOrder.OUTLET_ID,
                    OUTLET_ME = excelOrder.OUTLET_NAME, // OUTLET NAME maps to OUTLET_ME
                    ORDER_TYPE = excelOrder.ORDER_TYPE,
                    ORDER_ID_IN_CHATBOT = excelOrder.ORDER_ID_IN_CHATBOT,
                    Latitude = excelOrder.Latitude,
                    Longitude = excelOrder.Longitude,
                    Ko_Week = excelOrder.Ko_Week,
                    Ko_Month = excelOrder.Ko_Month,
                    IsOutletGeolocation = false, // Always 0 as per requirements
                    CreatedAt = processStartTime
                };
                
                // Parse ORDER_DATE and ORDER_TIME
                ParseDateAndTime(excelOrder, orderData);
                
                transformedData.Add(orderData);
                
                if ((i + 1) % 1000 == 0)
                {
                    progress?.Report($"Transformed {i + 1} records...");
                }
            }
            
            progress?.Report($"Successfully transformed {transformedData.Count} records.");
            return transformedData;
        }
        
        private void ParseDateAndTime(ExcelOrderData excelOrder, OrderData orderData)
        {
            // Parse ORDER_DATE (DD-MM-YYYY format)
            if (!string.IsNullOrEmpty(excelOrder.ORDER_DATE))
            {
                if (DateTime.TryParseExact(excelOrder.ORDER_DATE, "dd-MM-yyyy", 
                    CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime orderDate))
                {
                    orderData.ORDER_DATE = orderDate;
                }
            }
            
            // Parse ORDER_TIME
            if (!string.IsNullOrEmpty(excelOrder.ORDER_TIME))
            {
                // Try different time formats
                var timeFormats = new[] { @"hh\:mm\:ss", @"hh\:mm", @"h\:mm\:ss", @"h\:mm" };

                foreach (var format in timeFormats)
                {
                    if (TimeSpan.TryParseExact(excelOrder.ORDER_TIME, format,
                        CultureInfo.InvariantCulture, out TimeSpan orderTime))
                    {
                        orderData.ORDER_TIME = orderData.ORDER_DATE.HasValue
                            ? orderData.ORDER_DATE.Value.Add(orderTime)
                            : null;
                        break;
                    }
                }
            }

            // Combine ORDER_DATE and ORDER_TIME to create OrderDate_Time
            if (orderData.ORDER_DATE.HasValue)
            {
                var combinedDateTime = orderData.ORDER_DATE.Value.Date;
                
                if (orderData.ORDER_TIME.HasValue)
                {
                    combinedDateTime = combinedDateTime.Add(orderData.ORDER_TIME.Value.TimeOfDay);
                }
                
                orderData.OrderDate_Time = combinedDateTime;
            }
        }
    }
}
