using Core.Models.DTOs;
using Libraries.CommonModels;

namespace Core.Repositories
{
    public interface ILocationsRepository
    {
        Task<List<GeoHierarchy>> GetGeoHierarchy(long companyId, List<long> beatIds);
        Task<List<LocationMinModel>> GetLocationMinModels(long companyId, IEnumerable<long> locationIds);
        Task<Dictionary<long, EntityMinWithErp>> GetOutletDictionary(List<long> outletIds);
    }
}
