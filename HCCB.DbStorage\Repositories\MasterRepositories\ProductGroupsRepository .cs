﻿using Core.Models.DTOs;
using Core.Repositories;
using HCCB.DbStorage.DbContexts;
using Library.StringHelpers;
using Microsoft.Data.SqlClient;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class ProductGroupsRepository : IProductGroupsRepository
    {
        private readonly MasterDbSqlDataReader _db;

        public ProductGroupsRepository(MasterDbSqlDataReader db)
        {
            _db = db;
        }

        /// <summary>
        /// Returns dictionary of ERPId → ProductGroupId for a company.
        /// </summary>
        public async Task<Dictionary<string, long>> GetProductGroupsDictionary(long companyId, List<string> erps)
        {
            const string sql = @"
                SELECT ERPId, Id 
                FROM FAProductGroups
                WHERE CompanyId = @companyId AND IsDeleted = 0 AND ERPId in ($$Ids$$)";

            var sqlParams = new List<SqlParameter> { new("companyId", companyId) };

            var result = await _db.GetModelFromQueryAsync<ProductGroupFlat>(queryString: sql, ids: erps, parameters: sqlParams);

            return result
                .Where(x => !string.IsNullOrWhiteSpace(x.ERPId))
                .GroupBy(x => x.ERPId.NormalizeCaps())
                .ToDictionary(
                    g => g.Key,
                    g => g.First().Id
                );
        }
    }
}
