﻿using HCCBProductWebjob.Configurations;
using HCCBProductWebjob.Processors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace HCCBProductWebjob
{
    class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((hostingContext, config) =>
                {

                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                         .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", optional: true, reloadOnChange: true)
                         .AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseConsoleLifetime();

            using var host = builder.Build();

            using (var scope = host.Services.CreateScope())
                await scope.ServiceProvider.GetRequiredService<ProductProcessor>().Process();

            using (var scope = host.Services.CreateScope())
                await scope.ServiceProvider.GetRequiredService<AlternateCategoryProcessor>().Process();
        }
    }
}
