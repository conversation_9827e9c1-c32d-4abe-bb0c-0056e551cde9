using HccbExportProcessor.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Text.Json;

namespace HccbExportProcessor
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((hostingContext, configBuilder) =>
                {
                    configBuilder
                        .AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
                        .AddEnvironmentVariables()
                        ;
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddAzureStorageQueues(c =>
                    {
                        c.BatchSize =
#if DEBUG
                          1;
#else
                          5;
#endif
                    });
                })
                .ConfigureServices((context, services) =>
                {
                    Console.WriteLine($"{JsonSerializer.Serialize(context.Configuration.AsEnumerable(), new JsonSerializerOptions
                    {
                        WriteIndented = true
                    })}");
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseSerilog((_, loggerConfiguration) =>
                {
                    Dependencies.LogSetUp(loggerConfiguration, _.Configuration);
                })
                .UseConsoleLifetime();
            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }
    }
}