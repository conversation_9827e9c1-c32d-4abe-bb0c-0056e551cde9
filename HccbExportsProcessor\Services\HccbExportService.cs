using HccbExportProcessor.Models;
using HCCB.DbStorage.DbModel.HccbDbModel;
using HCCB.DbStorage.Repositories.HccbRepositories;
using Library.StorageWriter.Reader_Writer;
using Core.Repositories;
using Core.Loggers;
using Core.Models.HccbDbModels;
using Core.Models;
using Opw.HttpExceptions;
using Libraries.CommonModels;
using HCCB.DbStorage.Repositories.MasterRepositories;

namespace HccbExportProcessor.Services
{
    public class HccbExportService
    {
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly IHccbExportRequestRepository _exportRequestRepository;
        private readonly HCCBBlobWriter _blobWriter;
        private readonly HCCBBlobReader _blobReader;
        private readonly IMaxerienceLogRepository _maxerienceLogRepository;
        private readonly IUserRepository _userRepository;
        private readonly ILocationsRepository _locationsRepository;
        private readonly IPdfExportService _pdfExportService;
        private const string ContainerName = "hccb-exports";

        public HccbExportService(
            ISlackLogHelper slackLogHelper,
            IHccbExportRequestRepository exportRequestRepository,
            IMaxerienceLogRepository maxerienceLogRepository,
            IUserRepository userRepository,
            ILocationsRepository locationsRepository,
            IPdfExportService pdfExportService,
            HCCBBlobWriter blobWriter,
            HCCBBlobReader blobReader)
        {
            _slackLogHelper = slackLogHelper;
            _exportRequestRepository = exportRequestRepository;
            _maxerienceLogRepository = maxerienceLogRepository;
            _userRepository = userRepository;
            _locationsRepository = locationsRepository;
            _pdfExportService = pdfExportService;
            _blobWriter = blobWriter;
            _blobReader = blobReader;
        }

        public async Task<bool> ProcessExportAsync(HccbExportQueueData queueData)
        {
            var exportRequest = await _exportRequestRepository.GetByIdAsync(queueData.ExportId);
            try
            {
                if (exportRequest == null)
                {
                    throw new NotFoundException($"Export request with ID {queueData.ExportId} not found");
                }

                await UpdateExportRequest(exportRequest, ExportStatus.Processing, executionStarted: true);

                var (parameters, error) = ValidateAndParseParameters(exportRequest.Parameters);

                if (parameters == null)
                {
                    var errorMessage = $"Invalid export parameters for request {exportRequest.Id}. Error: {error}";
                    throw new InvalidOperationException(errorMessage);
                }

                var logs = await _maxerienceLogRepository.GetLogsByFilterAsync(
                    exportRequest.CompanyId,
                    parameters.Subordinates,
                    parameters.OutletIds,
                    parameters.StartDateKey,
                    parameters.EndDateKey);

                if (logs == null || !logs.Any())
                {
                    await GenerateAndSaveEmptyPdf(exportRequest, "No logs found for the given criteria");
                    return true;
                }

                // Get all scenes for these logs
                var logIds = logs.Select(l => l.Id).ToList();
                var scenes = await _maxerienceLogRepository.GetScenesByLogIdsAsync(logIds);

                var userNames = await _userRepository.GetUserDictionary(logs.Select(l => l.UserId).Distinct().ToList());
                var outletNames = await _locationsRepository.GetOutletDictionary(logs.Select(l => l.OutletId).Distinct().ToList());
                await GenerateAndSavePdf(exportRequest, logs, scenes, userNames, outletNames);
                return true;
            }
            catch (Exception ex)
            {
                if (exportRequest != null)
                {
                    await UpdateExportRequest(
                        exportRequest,
                        ExportStatus.Failed,
                        $"Unexpected error: {ex.Message}",
                        executionCompleted: true);
                }
                return false;
            }
        }

        private (ExportParameters Parameters, string Error) ValidateAndParseParameters(string parametersJson)
        {
            var parameters = System.Text.Json.JsonSerializer.Deserialize<ExportParameters>(parametersJson);
            return parameters == null ? (null, "Invalid parameters") : (parameters, null);
        }

        private async Task GenerateAndSaveEmptyPdf(HccbExportRequest exportRequest, string message = "No visits found for the selected criteria.")
        {
            var emptyPdfBytes = _pdfExportService.GenerateEmptyPdf(message);
            var blobName = $"UserOutletVisit/export_{exportRequest.Id}.pdf";
            var blobUrl = await UploadPdfToBlobStorage(blobName, emptyPdfBytes);

            await UpdateExportRequest(
                exportRequest,
                ExportStatus.Completed,
                blobUrl: blobUrl,
                executionCompleted: true);
        }

        private async Task GenerateAndSavePdf(HccbExportRequest exportRequest, List<MaxerienceLogMin> logs, List<Scenes> scenes, Dictionary<long, EntityMinWithErp> userNames, Dictionary<long, EntityMinWithErp> outletNames)
        {
            var pdfBytes = await _pdfExportService.GenerateExportPdfAsync(logs, scenes, userNames, outletNames);
            var blobName = $"UserOutletVisit/export_{exportRequest.Id}.pdf";
            var blobUrl = await UploadPdfToBlobStorage(blobName, pdfBytes);

            await UpdateExportRequest(
                exportRequest,
                ExportStatus.Completed,
                blobUrl: blobUrl,
                executionCompleted: true);
        }

        private async Task<string> UploadPdfToBlobStorage(string blobName, byte[] fileContent)
        {
            await _blobWriter.UploadByteArrayAsync(
                containerName: ContainerName,
                name: blobName,
                fileBytes: fileContent,
                contentType: "application/pdf");

            return _blobWriter.GetHccbPublicPath(ContainerName, blobName);
        }

        private async Task UpdateExportRequest(
            HccbExportRequest exportRequest,
            ExportStatus status,
            string errorMessage = null,
            string blobUrl = null,
            bool executionStarted = false,
            bool executionCompleted = false)
        {
            exportRequest.Status = status;

            if (errorMessage != null)
            {
                exportRequest.StatusMessage = errorMessage;
            }

            if (blobUrl != null)
            {
                exportRequest.OutputLink = blobUrl;
            }

            if (executionStarted)
            {
                exportRequest.ExecutionStartedAt = DateTime.UtcNow;
            }

            if (executionCompleted || status == ExportStatus.Completed || status == ExportStatus.Failed)
            {
                exportRequest.ExecutionCompletedAt = DateTime.UtcNow;
            }

            await _exportRequestRepository.UpdateAsync(exportRequest);
        }
    }
}
