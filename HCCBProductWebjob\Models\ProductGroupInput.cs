using System.ComponentModel.DataAnnotations;
namespace HCCBProductWebjob.Models
{
    public class ProductGroupInput
    {
        [Required]
        public string Name { get; set; }

        public string Description { get; set; }

        [Required]
        public string ERPId { get; set; }

        public string AttributeText1 { get; set; }

        [Required]
        public int Sequence { get; set; }

        public bool? IsDeleted { get; set; }
    }

    public class ProductGroupAttributes
    {
        public string ProductGroupErpId { get; set; }
    }
}
