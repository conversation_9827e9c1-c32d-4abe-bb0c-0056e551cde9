﻿using Core.Models;
using Core.Models.HccbDbModels;
using Core.Models.QueueModel;
using Core.Repositories;
using HCCB.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.HccbRepositories
{
    public class MaxerienceLogRepository : IMaxerienceLogRepository
    {
        private readonly WritableHccbDataContext writableHccbDataContext;
        private readonly HccbDbSqlDataReader _hccbDbSqlDataReader;

        public MaxerienceLogRepository(WritableHccbDataContext context, HccbDbSqlDataReader hccbDbSqlDataReader)
        {
            writableHccbDataContext = context;
            _hccbDbSqlDataReader = hccbDbSqlDataReader;
        }

        public async Task<List<TruMindsSyncQueueData>> GetUnsyncedLogsAsync(long companyId)
        {
            return await writableHccbDataContext.MaxerienceLog
                .Where(log => log.CompanyId == companyId && !log.IsSyncedToHccb)
                .Select(logRecord => new TruMindsSyncQueueData
                {
                    MaxerienceLogId = logRecord.Id,
                    CompanyId = logRecord.CompanyId,
                    MaxerienceSessionId = logRecord.MaxerienceSessionId
                })
                .ToListAsync();
        }

        public async Task UpdateSyncStatusAsync(long logId, bool isSynced)
        {
            var log = await writableHccbDataContext.MaxerienceLog.FindAsync(logId);
            if (log != null)
            {
                log.IsSyncedToHccb = isSynced;
                log.LastUpdatedAt = DateTime.UtcNow;
                await writableHccbDataContext.SaveChangesAsync();
            }
        }

        public async Task<MaxerienceLog> GetLogAsync(long logId)
        {
            return await writableHccbDataContext.MaxerienceLog
                .FirstOrDefaultAsync(x => x.Id == logId && !x.IsSyncedToHccb);
        }

        public async Task<List<Scenes>> GetScenesForLogAsync(long logId, long companyId)
        {
            return await writableHccbDataContext.Scenes
                .Where(s => s.CompanyId == companyId && s.MaxerienceLogId == logId)
                .ToListAsync();
        }

        public async Task<List<MaxerienceLogMin>> GetLogsByFilterAsync(long companyId,
            List<long> userIds,
            List<long> outletIds,
            long startDateKey,
            long endDateKey)
        {
            var query = writableHccbDataContext.MaxerienceLog.Where(x => x.CompanyId == companyId && x.QualifiedDate >= startDateKey && x.QualifiedDate <= endDateKey);

            if (userIds != null && userIds.Any())
            {
                query = query.Where(x => userIds.Contains(x.UserId));
            }

            if (outletIds != null && outletIds.Any())
            {
                query = query.Where(x => outletIds.Contains(x.OutletId));
            }

            return await query.Select(x => new MaxerienceLogMin
            {
                Id = x.Id,
                UserId = x.UserId,
                OutletId = x.OutletId,
                MaxerienceSessionStartTime = x.MaxerienceSessionStartTime
            }).ToListAsync();
        }

        public async Task<List<Scenes>> GetScenesByLogIdsAsync(List<long> logIds)
        {
            if (logIds == null || !logIds.Any())
            {
                return new List<Scenes>();
            }

            var query = @"
                SELECT Id, MaxerienceLogId, CompanyId, SceneTypeCode, SubSceneTypeCode, AssetId, CaptureTime, ImageSerialNumber, CreatedAt, LastUpdatedAt, CreationContext, ImageBlobPath, ImageGroupId
                FROM Scenes
                WHERE MaxerienceLogId IN ($$Ids$$)
                ORDER BY MaxerienceLogId, CaptureTime";

            return (await _hccbDbSqlDataReader.GetModelFromQueryAsync<Scenes>(query, logIds)).ToList();
        }
    }
}
