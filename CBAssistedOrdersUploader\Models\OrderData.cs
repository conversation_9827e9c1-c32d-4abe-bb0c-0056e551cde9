using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CBAssistedOrdersUploader.Models
{
    public class OrderData
    {
        [Key]
        public long S_No { get; set; }

        [MaxLength(100)]
        public string? SALES_POINT_ID { get; set; }

        public long? FA_User_Id { get; set; }

        [MaxLength(100)]
        public string? STL_ID { get; set; }

        [MaxLength(100)]
        public string? STL_ME { get; set; }

        [MaxLength(100)]
        public string? MARKET_AREA_ME { get; set; }

        [MaxLength(100)]
        public string? DISTRIBUTOR_ID { get; set; }

        [MaxLength(100)]
        public string? OUTLET_ID { get; set; }

        public string? OUTLET_ME { get; set; }

        [MaxLength(100)]
        public string? ORDER_TYPE { get; set; }

        public long? ORDER_ID_IN_CHATBOT { get; set; }

        public DateTime? ORDER_DATE { get; set; }

        public DateTime? ORDER_TIME { get; set; }

        public DateTime? OrderDate_Time { get; set; }

        public double? Latitude { get; set; }

        public double? Longitude { get; set; }

        public int? Ko_Week { get; set; }

        public int? Ko_Month { get; set; }

        public bool IsOutletGeolocation { get; set; } = false;

        public DateTime CreatedAt { get; set; }
    }
}
