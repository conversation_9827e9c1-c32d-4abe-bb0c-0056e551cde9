﻿using HCCB_ADLS.Core.Interfaces;
using HCCB_ADLS.Core.Models;
using HCCB_ADLS.DbStorage.DbContexts;
using HCCB_ADLS.DbStorage.MasterRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace HCCB_ADLS.DbStorage.MasterRepositories
{
    public class DistributorRepository : IDistributorRepository
    {

        private readonly MasterDbContext _db;

        public DistributorRepository(MasterDbContext db)
        {
            _db = db;
        }

        #region Private

        private IQueryable<Distributor> GetDistributorQueryable(long companyId, bool includeDeactivate = false)
        {
            return includeDeactivate ? _db.Distributors.Where(l => l.CompanyId == companyId && !l.Deleted) : _db.Distributors.Where(l => l.CompanyId == companyId && !l.Deleted && !l.IsDeactive);
        }

        #endregion Private


        public async Task<List<DistributorList>> GetDistributors(long companyId, bool includeDeactivate = false)
        {
            var outletsQuerable = GetDistributorQueryable(companyId, includeDeactivate);
            return await outletsQuerable.Select(p => new DistributorList()
            {
                Id = p.Id,
                Name = p.Name,
                ERPId = p.ErpId,
                AttributeText2 = p.AttributeText2 
            }).AsNoTracking().ToListAsync();

        }
    }
}
