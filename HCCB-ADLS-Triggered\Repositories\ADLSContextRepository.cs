﻿using HCCB_ADLS_Triggered.DbContexts;
using HCCB_ADLS_Triggered.Helper;
using HCCB_ADLS_Triggered.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace HCCB_ADLS_Triggered.Repositories
{
    public class ADLSContextRepository : IADLSContextRepository
    {
        private readonly HccbDbContext _hccbDb;

        public ADLSContextRepository(HccbDbContext hccbDb)
        {
            _hccbDb = hccbDb;
        }
        public async Task<List<QueueData>> GetADLSContext(long companyId)
        {
            try
            {
                var dbData = await _hccbDb.ADLSContextDetails
                        .Include(ad => ad.MetricValues)
                        .Where(x => x.CompanyId == companyId && !x.IsDeactive && x.ContextEnum == HccbDbModels.ContextEnum.Metric)
                        .ToListAsync();

                var todatDate = DateTime.UtcNow;
                var dateDirectory = $"{todatDate.Year}/{todatDate.Month.ToString("00")}/{todatDate.Day.ToString("00")}/";

                var queueDataList = dbData.Select(ad => new QueueData
                {
                    DirectoryPath = ad.AddDateDirectory ? $"{ad.DirectoryPath}{dateDirectory}" : ad.DirectoryPath,
                    FileName = ad.FileName,
                    AccountName = ad.AccountName,
                    FileSystemName = ad.FileSystemName,
                    IsfromSAS = ad.IsfromSAS,
                    HeaderNames = string.IsNullOrEmpty(ad.HeaderNames) ? null : JsonSerializer.Deserialize<List<string>>(ad.HeaderNames),
                    RowConditions = string.IsNullOrEmpty(ad.RowConditions) ? null : JsonSerializer.Deserialize<List<Condition>>(ad.RowConditions),
                    Metrics = ad.MetricValues.Select(m => new Metric
                    {
                        MetricErpId = m.MetricErpId,
                        MetricEntityType = m.MetricEntityType,
                        MetricErpIdHeaderName = m.MetricErpIdHeaderName,
                        MetricValue = string.IsNullOrEmpty(m.MetricValue) ? null : JsonSerializer.Deserialize<MetricValue>(m.MetricValue)
                    }).ToList(),
                    ExternalApiBaseAddress = ad.ExternalApiBaseAddress,
                    ExternalAPIUserName = ad.ExternalAPIUserName,
                    ExternalAPIPassword = ad.ExternalAPIPassword,
                    ExternalApiEndPoint = ad.ExternalApiEndPoint,
                    ConsiderAllHeaders = ad.ConsiderAllHeaders,
                    CompanyId = ad.CompanyId
                }).ToList();

                return queueDataList;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error Occured: {ex.Message}");
                throw;
            }
        }

        public async Task<List<QueueData>> GetADLSInvoiceContext(long companyId)
        {
            var dbData = await _hccbDb.ADLSContextDetails
                    .Where(x => x.CompanyId == companyId && !x.IsDeactive && x.ContextEnum == HccbDbModels.ContextEnum.NonFAInvoice)
                    .ToListAsync();

            var indiaOffset = new TimeSpan(5, 30, 0); // Offset for Indian time zone (UTC+5:30)
            var currentDateTime = DateTime.UtcNow.Add(indiaOffset);
            var dateDirectory = $"{currentDateTime.Year}/{currentDateTime.Month.ToString("00")}/{currentDateTime.Day.ToString("00")}/";

            var queueDataList = dbData.Select(ad => new QueueData
            {
                DirectoryPath = ad.AddDateDirectory ? $"{ad.DirectoryPath}{dateDirectory}" : ad.DirectoryPath,
                FileName = ad.FileName,
                AccountName = ad.AccountName,
                FileSystemName = ad.FileSystemName,
                IsfromSAS = ad.IsfromSAS,
                HeaderNames = string.IsNullOrEmpty(ad.HeaderNames) ? null : JsonSerializer.Deserialize<List<string>>(ad.HeaderNames),
                RowConditions = string.IsNullOrEmpty(ad.RowConditions) ? null : JsonSerializer.Deserialize<List<Condition>>(ad.RowConditions),
                ExternalApiBaseAddress = ad.ExternalApiBaseAddress,
                ExternalAPIUserName = ad.ExternalAPIUserName,
                ExternalAPIPassword = ad.ExternalAPIPassword,
                ExternalApiEndPoint = ad.ExternalApiEndPoint,
                ConsiderAllHeaders = ad.ConsiderAllHeaders,
                CompanyId = ad.CompanyId
            }).ToList();

            return queueDataList;
        }
    }
}
