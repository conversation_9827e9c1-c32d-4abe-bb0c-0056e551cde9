using CBAssistedOrdersUploader.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System.IO;
using System.Windows;

namespace CBAssistedOrdersUploader
{
    public partial class MainWindow : Window
    {
        private readonly ExcelReaderService _excelReaderService;
        private readonly DataTransformationService _dataTransformationService;
        private readonly DatabaseService _databaseService;
        private readonly ILogger<MainWindow> _logger;
        private CancellationTokenSource? _cancellationTokenSource;
        
        public MainWindow()
        {
            InitializeComponent();
            
            // Setup dependency injection
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            services.AddTransient<ExcelReaderService>();
            services.AddTransient<DataTransformationService>();
            services.AddTransient<DatabaseService>();
            
            var serviceProvider = services.BuildServiceProvider();
            
            _excelReaderService = serviceProvider.GetRequiredService<ExcelReaderService>();
            _dataTransformationService = serviceProvider.GetRequiredService<DataTransformationService>();
            _databaseService = serviceProvider.GetRequiredService<DatabaseService>();
            _logger = serviceProvider.GetRequiredService<ILogger<MainWindow>>();
            
            LogMessage("Application started. Please configure connection settings and select an Excel file.");

            // Wire up text changed events
            ConnectionStringTextBox.TextChanged += ConnectionStringTextBox_TextChanged;
            TableNameTextBox.TextChanged += TableNameTextBox_TextChanged;
        }
        
        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx",
                Title = "Select Excel File"
            };
            
            if (openFileDialog.ShowDialog() == true)
            {
                ExcelFilePathTextBox.Text = openFileDialog.FileName;
                LogMessage($"Selected file: {openFileDialog.FileName}");
                UpdateUploadButtonState();
            }
        }
        
        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(ConnectionStringTextBox.Text))
            {
                MessageBox.Show("Please enter a connection string.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrWhiteSpace(TableNameTextBox.Text))
            {
                MessageBox.Show("Please enter a table name.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            TestConnectionButton.IsEnabled = false;
            StatusText.Text = "Testing connection...";
            LogMessage("Testing database connection...");
            
            try
            {
                var isValid = await _databaseService.TestConnectionAsync(
                    ConnectionStringTextBox.Text.Trim(), 
                    TableNameTextBox.Text.Trim());
                
                if (isValid)
                {
                    MessageBox.Show("Connection successful! Table exists.", "Success", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    LogMessage("Database connection test successful.");
                    StatusText.Text = "Connection test successful";
                }
                else
                {
                    MessageBox.Show("Connection failed or table does not exist.", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    LogMessage("Database connection test failed or table does not exist.");
                    StatusText.Text = "Connection test failed";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Connection test failed: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LogMessage($"Connection test error: {ex.Message}");
                StatusText.Text = "Connection test failed";
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
                UpdateUploadButtonState();
            }
        }
        
        private async void UploadButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInputs())
                return;
            
            _cancellationTokenSource = new CancellationTokenSource();
            var processStartTime = DateTime.UtcNow;
            
            SetUIState(false);
            ProgressBar.Value = 0;
            LogTextBox.Clear();
            
            try
            {
                LogMessage("Starting upload process...");
                StatusText.Text = "Processing...";
                
                var progress = new Progress<string>(message =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        ProgressLabel.Content = message;
                        LogMessage(message);
                    });
                });
                
                // Step 1: Read Excel file
                LogMessage("Step 1: Reading Excel file...");
                var excelData = await _excelReaderService.ReadExcelFileAsync(
                    ExcelFilePathTextBox.Text.Trim(), progress);
                
                ProgressBar.Value = 33;
                
                // Step 2: Transform data
                LogMessage("Step 2: Transforming data...");
                var transformedData = _dataTransformationService.TransformData(
                    excelData, processStartTime, progress);
                
                ProgressBar.Value = 66;
                
                // Step 3: Bulk insert
                LogMessage("Step 3: Inserting data into database...");
                var insertedCount = await _databaseService.BulkInsertOrdersAsync(
                    ConnectionStringTextBox.Text.Trim(),
                    TableNameTextBox.Text.Trim(),
                    transformedData,
                    progress,
                    _cancellationTokenSource.Token);
                
                ProgressBar.Value = 100;
                
                var successMessage = $"{insertedCount} Number of records Inserted Successfully with CreatedAt Stamp = {processStartTime:yyyy-MM-dd HH:mm:ss} UTC";
                
                MessageBox.Show(successMessage, "Upload Successful", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                LogMessage(successMessage);
                StatusText.Text = "Upload completed successfully";
            }
            catch (OperationCanceledException)
            {
                LogMessage("Upload operation was cancelled.");
                StatusText.Text = "Upload cancelled";
            }
            catch (Exception ex)
            {
                var errorMessage = $"Upload failed: {ex.Message}";
                var logFilePath = await SaveErrorLogAsync(ex, processStartTime);
                
                MessageBox.Show($"{errorMessage}\n\nDetailed error log saved to:\n{logFilePath}", 
                    "Upload Failed", MessageBoxButton.OK, MessageBoxImage.Error);
                
                LogMessage(errorMessage);
                LogMessage($"Error log saved to: {logFilePath}");
                StatusText.Text = "Upload failed";
            }
            finally
            {
                SetUIState(true);
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }
        
        private bool ValidateInputs()
        {
            if (string.IsNullOrWhiteSpace(ConnectionStringTextBox.Text))
            {
                MessageBox.Show("Please enter a connection string.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(TableNameTextBox.Text))
            {
                MessageBox.Show("Please enter a table name.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(ExcelFilePathTextBox.Text) || 
                !File.Exists(ExcelFilePathTextBox.Text))
            {
                MessageBox.Show("Please select a valid Excel file.", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void UpdateUploadButtonState()
        {
            UploadButton.IsEnabled = !string.IsNullOrWhiteSpace(ConnectionStringTextBox.Text) &&
                                   !string.IsNullOrWhiteSpace(TableNameTextBox.Text) &&
                                   !string.IsNullOrWhiteSpace(ExcelFilePathTextBox.Text) &&
                                   File.Exists(ExcelFilePathTextBox.Text);
        }
        
        private void SetUIState(bool enabled)
        {
            ConnectionStringTextBox.IsEnabled = enabled;
            TableNameTextBox.IsEnabled = enabled;
            BrowseButton.IsEnabled = enabled;
            TestConnectionButton.IsEnabled = enabled;
            UploadButton.IsEnabled = enabled && ValidateInputs();
        }
        
        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var logEntry = $"[{timestamp}] {message}";
            
            LogTextBox.AppendText(logEntry + Environment.NewLine);
            LogTextBox.ScrollToEnd();
            
            _logger.LogInformation(message);
        }
        
        private async Task<string> SaveErrorLogAsync(Exception ex, DateTime processStartTime)
        {
            var logFileName = $"CBAssistedOrdersUploader_Error_{processStartTime:yyyyMMdd_HHmmss}.log";
            var logFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), logFileName);
            
            var logContent = $"CB Assisted Orders Uploader - Error Log\n" +
                           $"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                           $"Process Start Time: {processStartTime:yyyy-MM-dd HH:mm:ss} UTC\n" +
                           $"Connection String: {ConnectionStringTextBox.Text}\n" +
                           $"Table Name: {TableNameTextBox.Text}\n" +
                           $"Excel File: {ExcelFilePathTextBox.Text}\n\n" +
                           $"Error Details:\n" +
                           $"Message: {ex.Message}\n" +
                           $"Stack Trace:\n{ex.StackTrace}\n\n";
            
            if (ex.InnerException != null)
            {
                logContent += $"Inner Exception:\n" +
                            $"Message: {ex.InnerException.Message}\n" +
                            $"Stack Trace:\n{ex.InnerException.StackTrace}\n";
            }
            
            await File.WriteAllTextAsync(logFilePath, logContent);
            return logFilePath;
        }
        
        private void ConnectionStringTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateUploadButtonState();
        }
        
        private void TableNameTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateUploadButtonState();
        }
    }
}
