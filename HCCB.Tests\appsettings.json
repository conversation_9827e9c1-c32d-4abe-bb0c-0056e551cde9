{"ASPNETCORE_ENVIRONMENT": "Development", "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;", "Logging": {"IncludeScopes": false, "LogLevel": {"Default": "Warning"}}, "SlackChannel": {"reports": "reports-dev", "dsr": "DSRMSR-dev"}, "Loki": {"BaseUrl": "https//loki.fieldassist.io/", "UserName": "default", "Password": "bG9raSNmMmtAZGVmYXVsdA=="}, "AppSettings": {"Deployment": "dev", "portalBaseUrl": "https://debug.fieldassist.in", "cosmosReportingBaseUrl": "https://fasolutionv3-nsappapi-mt.azurewebsites.net/", "DMSExternalApiURL": "https://dms-debug.fieldassist.io/", "DMSExternalApiUserName": "fa_nodejs_dms_server", "DMSExternalApiPassword": "d98c2d56eb0f3218dc8d524d697f42da"}, "HCCBBlobPassword": "SFA123", "HCCBClientAPIToken": "SENDQl9GaWVsZFRlc3RfVUFUOldpVGNMQVdiaWl6cl40Rm9xZlBT", "TempNonFAInvoiceAPIFieldUserErp": "Train1", "TelegramBotToken": "**********************************************", "Values": {"TelegramBotToken": "**********************************************"}, "HCCBContainerName": "dms-uat-test", "ConnectionStrings": {"WritableHccbDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=hccb-fa-data;user id=hccbproductrecom;password=***********************************;", "HccbDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=hccb-fa-data;user id=hccbproductrecom;password=***********************************;", "HCCBFaDataConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=hccb-fa-data;user id=hccbproductrecom;password=***********************************;", "HCCBStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=hccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=hccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "DMSDbConnectionString": "Server=fadmsdebug.postgres.database.azure.com;Database=fa_dms_debug;Port=5432;User Id=fadmsdebug@fadmsdebug;Password=**********;SSL Mode=Require;Trust Server Certificate=true;", "TransactionDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Transactions;user id=readonlylogin;password=debugger@adonlyJune;", "WritableTransactionDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Database=FA_Transactions;UserID=Azure_ReadWrite;Password=****************;Trusted_Connection=False;Encrypt=True;ConnectionTimeout=30;MaxPoolSize=1000;", "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=faappapiv3debug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;", "MasterDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Database=F2KLocationsNetworkV3_HCCB;User ID=f2kadmin;Password=*************************;Trusted_Connection=False;Encrypt=True;Connection Timeout=30;Max Pool Size=1000;MultipleActiveResultSets=true;", "WritableMasterDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=Test_F2KLocationsNetwork;user id=debug_write;password=************************;", "ReportDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Reports;user id=readonlylogin;password=debugger@adonlyJune;", "LiteDMSDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_LiteDMS;user id=debug_write;password=************************;", "AzureWebJobsDashboard": "DefaultEndpointsProtocol=https;AccountName=faappapiv3debug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "DashboarAPIConnectionString": "https://fa-dashboard-apis.fieldassist.io"}, "hccbAPIhelper": {"tokenUrl_UAT": "https://dm-em.informaticacloud.com/authz-service/oauth/token", "clientId_UAT": "5Elco2GNJ0rbU6QYPmvDmm", "clientSecret_UAT": "FVDIa7S06", "accessToken_UAT": "eyJraWQiOiI1RThUc2M5S0RacGJUOHNEdmtHSTI4IiwidHlwIjoiSldUIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bkZ7wdTKgHQ06P17u5z6pV6F-maeQhLfAdVSObtI9ZNX8xFH3zM817c9iTsOL-tjvFZmXfoi3g27EJuz5FMhQvVo9hN8566cePXmHHU9Vx66-9NqXdTWhbjN_NDniWa8ney4lbPW2_H95OcprOtOqwvl_17GV09QtOHAYXfg0whUC7Q1rwyLueS6r1emZM0jQLDEI8j5OmeyRwSZZm0qX9MXdxg49yzC9jUcJy5a6yuURuSchScl0MyvJGYJqhCtZp8uxqoLVBw77l4xZSF-rioF0UNAkJlYv5OhaNI_y2-vmt2IdUpYq6i15Vbwf-kmWhZH7vA60z4uWAuBbGtcMQ", "tokenUrl": "https://dm-em.informaticacloud.com/authz-service/oauth/token", "clientId": "2FSPmiSojgzgMikcTSQ50m", "clientSecret": "iy9beViCK", "accessToken": "MkZTUG1pU29qZ3pnTWlrY1RTUTUwbTppeTliZVZpQ0s=", "ctsPlacementUrl_UAT": "https://emw1-apigw.dm-em.informaticacloud.com/t/0i8me60sbullrpgbyrzuuy.com/Prc_CAI_CTS_Placement_Request", "ctsPlacementUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Prc_CAI_CTS_Placement_Request", "ctsTransferUrl_UAT": "https://emw1-apigw.dm-em.informaticacloud.com/t/0i8me60sbullrpgbyrzuuy.com/Prc_CAI_CTS_Transfer_Request", "ctsTransferUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Prc_CAI_CTS_Transfer_Request", "ctsBreakdownUrl_UAT": "https://emw1-apigw.dm-em.informaticacloud.com/t/0i8me60sbullrpgbyrzuuy.com/Prc_CAI_CTS_Breakdown_Request", "ctsBreakdownUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Prc_CAI_CTS_Breakdown_Request", "ctsRemovalUrl_UAT": "https://emw1-apigw.dm-em.informaticacloud.com/t/0i8me60sbullrpgbyrzuuy.com/Prc_CAI_CTS_Removal_Request", "ctsRemovalUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Prc_CAI_CTS_Removal_Request"}, "Slack": {"ApiToken": "******************************************************"}}