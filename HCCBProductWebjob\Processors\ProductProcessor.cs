﻿using Core.Abstracts;
using Core.Loggers;
using HCCBProductWebjob.Configurations;
using HCCBProductWebjob.Helpers;
using HCCBProductWebjob.Models;
using HCCBProductWebjob.Repositories;
using HCCBProductWebjob.Services.Interfaces;
using Library.ResponseHelpers;
using System.Text.RegularExpressions;

namespace HCCBProductWebjob.Processors
{
    public class ProductProcessor(HCCBApiValues hCCBApiValues,
        IGetOAuthTokenService getOAuthTokenService,
        IHCCBGetItemsService hCCBGetItemsService,
        IFAProductService fAProductService,
        IProductGroupService productGroupService,
        IPageNumberService pageNumberService,
        IFACategoriesService fACategoriesService,
        IHCCBAcknowlegementService hCCBAcknowlegementService,
        IPickListMasterRepository pickListMasterRepository,
        ISlackLogHelper slackLogHelper,
        FileLogger fileLogger) : ProcessorBase(fileLogger, slackLogHelper)
    {
        private readonly HCCBApiValues hCCBApiValues = hCCBApiValues;
        private readonly IGetOAuthTokenService getOAuthTokenService = getOAuthTokenService;
        private readonly IHCCBGetItemsService hCCBGetItemsService = hCCBGetItemsService;
        private readonly IFAProductService fAProductService = fAProductService;
        private readonly IFACategoriesService fACategoriesService = fACategoriesService;
        private readonly IPageNumberService pageNumberService = pageNumberService;
        private readonly IHCCBAcknowlegementService hCCBAcknowlegementService = hCCBAcknowlegementService;
        private readonly IPickListMasterRepository pickListMasterRepository = pickListMasterRepository;
        private readonly IProductGroupService productGroupService = productGroupService;
        private const string ProcessorName = "Product Master Processor";

        private List<ItemMaster> hccbItemMaster = new List<ItemMaster>();
        private string accessToken = string.Empty;

        protected override string LogHeader
        {
            get; set;
        }

        protected override string GetChannelId()
        {
            return Dependencies.ChannelId;
        }

        protected override string GetProcessorName()
        {
            return ProcessorName;
        }

        protected override void _ValidateArguments(params object[] args)
        {
            return;
        }

        protected override async Task _Process(params object[] args)
        {
            await GetDataFromHCCB();
            var csvUploadTask = MakeCsvAndSendOnSlack(hccbItemMaster);
            await ProcessData();
            await csvUploadTask;
        }

        #region MainFunctions
        public async Task GetDataFromHCCB()
        {
            var hccbProductMaster = new List<HccbItemMaster>();
            accessToken = (await getOAuthTokenService.GetOAuthToken(hCCBApiValues.Username, hCCBApiValues.Password)).access_token;
            var pagesNSessions = await pageNumberService.ProcessGetPageCount(accessToken, hCCBApiValues.ItemInterfaceId);
            int pageCount = int.Parse(pagesNSessions.Output.row.col1);
            int totalRecords = int.Parse(pagesNSessions.Output.row.total_no_of_record);
            if (totalRecords > 0)
            {
                for (int i = 1; i <= pageCount; i++)
                {
                    var response = await hCCBGetItemsService.ProcessCAIGetItemMaster(accessToken, i);
                    hccbProductMaster.Add(response);
                }
                hccbItemMaster = hccbProductMaster.SelectMany(x => x.Output.row).ToList();
            }
            else
            {
                fileLogger.WriteLine("Unfortunately No Products to Create/Update today!");
            }
        }

        public async Task ProcessData()
        {
            if (hccbItemMaster.Count > 0)
            {
                var hccbItemMasterGrouped = hccbItemMaster.GroupBy(x => x.item_id)
                                                .Select(group => group.First()).ToList();
                if (hccbItemMasterGrouped.Count < hccbItemMaster.Count)
                {
                    fileLogger.WriteLine($"{hccbItemMaster.Count - hccbItemMasterGrouped.Count} Duplicate Records Found and Removed in HCCB Item Master!");
                    hccbItemMaster = hccbItemMasterGrouped;
                }

                fileLogger.WriteLine("Getting existing divisions and categories!");
                var getFACategories = await fACategoriesService.GetCategories();
                fileLogger.WriteLine("Getting existing products!");
                var getProductDetails = await fAProductService.GetProductsHeirarchy();
                var faProductDivisions = getProductDetails.Select(x => x.ProductDivisionName).ToList();
                var faPrimaryCategories = getProductDetails.Select(x => x.PrimaryCategoryName).ToList();
                var faSecondaryCategories = getProductDetails.Select(x => x.SecondaryCategoryName).ToList();
                var concatenatedHierarchies = getProductDetails.Select(x => $"{x.SecondaryCategoryName}_{x.PrimaryCategoryName}_{x.ProductDivisionName}").ToList();
                var creatingCategories = new List<CategoryModel>();
                var updatePrimaryCategories = new List<ChangeBrandModel>();
                var updateSecondaryCategories = new List<ChangeCategoryModel>();
                var updateProducts = new List<UpdateProduct>();
                var createProducts = new List<FACreateProduct>();
                var reactivatedProducts = new List<string>();

                var primaryCategoriesPickListMaster = await pickListMasterRepository.GetPrimaryCategoryDictionary();
                var secondaryCategoriesPickListMaster = await pickListMasterRepository.GetSecondaryCategoryDictionary();
                // Extract all EAN codes
                var allEanCodes = hccbItemMaster
                    .Select(i => i.ean_code)
                    .Where(e => !string.IsNullOrWhiteSpace(e))
                    .ToList();

                // Ensure groups exist
                await productGroupService.EnsureProductGroupsExist(allEanCodes, Dependencies.CompanyId);

                foreach (var item in hccbItemMaster)
                {
                    string lastSixDigits = item.item_id.Substring(item.item_id.Length - 6);
                    var isDeactive = getProductDetails.FirstOrDefault(x => x.ErpId == item.item_id)?.IsDeactive ?? false;

                    //Reactivatate Products
                    if (ConvertToBoolean(item.active) && isDeactive)
                    {
                        reactivatedProducts.Add(item.item_id);
                    }
                    //New Division
                    //CreateProductCategories
                    var mm_trademark_desc = primaryCategoriesPickListMaster.ContainsKey(item.mm_trademark_id.Trim()) ? primaryCategoriesPickListMaster[item.mm_trademark_id.Trim()] : primaryCategoriesPickListMaster["ZZZ"];
                    var mm_brand_desc = secondaryCategoriesPickListMaster.ContainsKey(item.mm_brand_id.Trim()) ? secondaryCategoriesPickListMaster[item.mm_brand_id.Trim()] : secondaryCategoriesPickListMaster["ZZZ"];
                    if (!faProductDivisions.Contains(item.category_desc) || !faPrimaryCategories.Contains(mm_trademark_desc.Trim()) || !faSecondaryCategories.Contains(mm_brand_desc.Trim()) || !concatenatedHierarchies.Contains($"{mm_brand_desc}_{mm_trademark_desc}_{item.category_desc}"))
                    {
                        creatingCategories.Add(new CategoryModel
                        {
                            ProductDivision = item.category_desc.Trim(),
                            Brand = mm_trademark_desc.Trim(),
                            BrandErpId = item.mm_trademark_id.Trim(),
                            Category = mm_brand_desc.Trim(),
                            CategoryErpId = item.brand_id.Trim(),
                            ProductDivisionErpId = item.category_id.Trim(),
                            StandardUnit = "CS"
                        });
                    }
                    //ChangeMappingCategories
                    //Update Primary Category of a Secondary Category
                    if (mm_trademark_desc.ToUpper().Normalize() != getFACategories.Where(x => x.Category.ToUpper().Normalize() == mm_brand_desc.Trim().ToUpper().Normalize()).Select(y => y.Brand).ToString().Trim().ToUpper().Normalize())
                    {

                        updatePrimaryCategories.Add(new ChangeBrandModel
                        {
                            Brand = mm_trademark_desc.Trim(),
                            Category = mm_brand_desc.Trim()
                        });
                    }
                    //ChangeBrandForCategory
                    //Update Secondary Category of a Product
                    if (mm_brand_desc.ToUpper().Normalize() != getProductDetails.Where(x => x.ErpId.ToUpper().Normalize() == item.item_id.Trim().ToUpper().Normalize()).Select(y => y.SecondaryCategoryName).ToString().Trim().ToUpper().Normalize())
                    {
                        updateSecondaryCategories.Add(new ChangeCategoryModel
                        {
                            Category = mm_brand_desc.Trim(),
                            PrimaryCategory = mm_trademark_desc.Trim(),
                            ProductDivision = item.category_desc.Trim(),
                            ProductErpId = item.item_id.Trim()
                        });
                    }
                    //Update Products
                    if (getProductDetails.Select(x => x.ErpId).ToList().Contains(item.item_id.ToString().Trim().Normalize()))
                    {
                        updateProducts.Add(new UpdateProduct
                        {
                            Deactivated = !ConvertToBoolean(item.active),
                            Name = $"{mm_brand_desc.Trim()} {item.pack_type_id.Trim()} {item.secondary_pack_desc.Trim()} [{lastSixDigits}]",
                            VariantName = mm_brand_desc.Trim(),
                            Display_MRP = item.mrp,
                            MRP = double.TryParse(item.mrp, out double mrp) ? mrp : 0.0,
                            Price = 0,
                            Unit = "EA",
                            LocalName = item.short_name.Trim(),
                            StandardUnitConversionFactor = double.TryParse(item.bpc, out double standardUnitConversionFactor) ?
                            standardUnitConversionFactor != 0 ? standardUnitConversionFactor : 1.0 :
                            1.0,
                            SuperUnitConversionFactor = double.TryParse(item.uc_conversion, out double superUnitConversionFactor) ?
                            superUnitConversionFactor != 0 ? superUnitConversionFactor : 1.0 :
                            1.0,
                            Description = item.kind_desc.Trim(),
                            ProductERPId = item.item_id,
                            HSNCodeStr = item.hsn_code.Trim(),
                            HSNCode = int.TryParse(item.hsn_code, out int hsnCode) ? hsnCode : 0,
                            ShelfLifeInDays = Convert.ToInt32(item.shelf_life),
                            AttributeDate1 = !string.IsNullOrEmpty(item.last_modify_date) ? DateTime.Parse(item.last_modify_date) : null,
                            AttributeDate2 = !string.IsNullOrEmpty(item.created_date) ? DateTime.Parse(item.created_date) : null,
                            AttributeNumber1 = double.TryParse(item.type, out double attributeNumber1) ? attributeNumber1 : 0.0,
                            AttributeNumber2 = double.TryParse(item.pc_conversion, out double attributeNumber2) ? attributeNumber2 : 0.0,
                            AttributeText1 = item.beverage_form_id.Trim(),
                            AttributeText2 = item.flavour_desc.Trim(),
                            PackSize = item.secondary_pack_desc.Trim(),
                            P1 = item.item_group_id.Trim(),
                            P2 = item.pack_form_desc.Trim(),
                            P3 = item.secondary_rc.Trim(),
                            P4 = item.serve_type_desc.Trim(),
                            P5 = item.varity_desc.Trim(),
                            Category2 = item.ean_code.Trim(),
                            MBQ = 1,
                            ProductGroupCode = item.ean_code.Trim(),
                        });
                    }
                    //CreateProduct
                    if (!getProductDetails.Select(x => x.ErpId).ToList().Contains(item.item_id.ToString().Trim().Normalize()))
                    {
                        createProducts.Add(new FACreateProduct
                        {
                            ProductDivision = item.category_desc.Trim(),
                            Brand = mm_trademark_desc.Trim(),
                            Category = mm_brand_desc.Trim(),
                            Name = $"{mm_brand_desc.Trim()} {item.pack_type_id.Trim()} {item.secondary_pack_desc.Trim()} [{lastSixDigits}]",
                            Display_MRP = item.mrp,
                            MRP = decimal.TryParse(item.mrp, out decimal mrp) ? mrp : 0.0m,
                            Price = 0,
                            Unit = "EA",
                            LocalName = item.short_name.Trim(),
                            StandardUnitConversionFactor = double.TryParse(item.bpc, out double standardUnitConversionFactor) ?
                            standardUnitConversionFactor != 0 ? standardUnitConversionFactor : 1.0 :
                            1.0,
                            SuperUnitConversionFactor = double.TryParse(item.uc_conversion, out double superUnitConversionFactor) ?
                            superUnitConversionFactor != 0 ? superUnitConversionFactor : 1.0 :
                            1.0,
                            Description = item.kind_desc.Trim(),
                            ProductERPId = $"{item.item_id.Trim()}",
                            HSNCodeStr = item.hsn_code.Trim(),
                            HSNCode = int.TryParse(item.hsn_code, out int hsnCode) ? hsnCode : 0,
                            ShelfLifeInDays = int.TryParse(item.shelf_life, out int shelfLife) ? shelfLife : 0,
                            AttributeDate1 = !string.IsNullOrEmpty(item.last_modify_date) ? DateTime.Parse(item.last_modify_date) : null,
                            AttributeDate2 = !string.IsNullOrEmpty(item.created_date) ? DateTime.Parse(item.created_date) : null,
                            AttributeNumber1 = double.TryParse(item.type, out double attributeNumber1) ? attributeNumber1 : 0.0,
                            AttributeNumber2 = double.TryParse(item.pc_conversion, out double attributeNumber2) ? attributeNumber2 : 0.0,
                            AttributeText1 = item.beverage_form_id.Trim(),
                            AttributeText2 = item.flavour_desc.Trim(),
                            P1 = item.item_group_id.Trim(),
                            P2 = item.pack_form_desc.Trim(),
                            P3 = item.secondary_rc.Trim(),
                            P4 = item.serve_type_desc.Trim(),
                            P5 = item.varity_desc.Trim(),
                            Category2 = item.ean_code.Trim(),
                            MBQ = 1,
                            ProductGroupCode = item.ean_code.Trim(),
                        });
                    }
                }

                creatingCategories = creatingCategories.GroupBy(x => new { x.ProductDivision, x.Brand, x.Category }).Select(group => group.First()).ToList();
                updatePrimaryCategories = updatePrimaryCategories.GroupBy(x => new { x.Brand, x.Category }).Select(group => group.First()).ToList();
                updateSecondaryCategories = updateSecondaryCategories.GroupBy(x => new { x.ProductDivision, x.PrimaryCategory, x.Category, x.ProductErpId }).Select(group => group.First()).ToList();
                if (creatingCategories.Count > 0)
                {
                    fileLogger.WriteLine("Creating new Divisions or Primary/Secondary Categories");

                    var brandCreation = await fACategoriesService.CreateBrandForCategory(creatingCategories);

                    int total = 0, updated = 0, failed = 0, ignored = 0;
                    string pattern = @".*Total-(\d+), Updated-(\d+), Failed-(\d+), Ignored-(\d+)";

                    foreach (var apiResponse in brandCreation)
                    {
                        Match match = Regex.Match(apiResponse.Message, pattern);

                        if (match.Success)
                        {
                            total += int.Parse(match.Groups[1].Value);
                            updated += int.Parse(match.Groups[2].Value);
                            failed += int.Parse(match.Groups[3].Value);
                            ignored += int.Parse(match.Groups[4].Value);
                        }
                    }

                    int recievedCount = creatingCategories.Count();

                    string jobMessage = "Master APIs - Item Master Sync Completion Log- \r\n Product Hierarchy Creation (Divisions, PrimaryCategory & SecondaryCategory) -";
                    await _slackLogHelper.SendCustomSlackMessage(jobMessage, true, "", recievedCount, updated, failed, ignored);
                }

                //CreateCompanyProducts
                if (createProducts.Count > 0)
                {
                    fileLogger.WriteLine("Creating Products");
                    var newCreatedProducts = await fAProductService.CreateMultipleProducts(createProducts);
                    var recievedCount = createProducts.Count();
                    var failedCountProduct = newCreatedProducts.Where(x => x.ResponseStatus == ResponseStatus.Failure).Count();
                    var ignoredCountProduct = newCreatedProducts.Where(x => x.ResponseStatus == ResponseStatus.Ignored).Count();
                    var successCountProduct = newCreatedProducts.Where(x => x.ResponseStatus == ResponseStatus.Success).Count();
                    string jobMessage = "Master APIs - Item Master Sync Completion Log- \r\n Product Hierarchy Creation (Product Creation) -";
                    await _slackLogHelper.SendCustomSlackMessage(jobMessage, true, "", recievedCount, successCountProduct, failedCountProduct, ignoredCountProduct);

                    if (newCreatedProducts.Count > 0)
                    {
                        const int batchSize = 500;
                        for (int i = 0; i < newCreatedProducts.Count; i += batchSize)
                        {
                            List<ApiResponseMessage> batch = newCreatedProducts.Skip(i).Take(batchSize).ToList();
                            _ = hCCBAcknowlegementService.AcknowledgeItemMasterCreateProduct(accessToken, batch); // Fire and Forget
                        }
                    }
                }
                if (updatePrimaryCategories.Count > 0)
                {
                    fileLogger.WriteLine("Updating Primary Categories for secondary categories");

                    var changePrimary = await fACategoriesService.ChangeBrandForCategory(updatePrimaryCategories);

                    int total = 0, updated = 0, failed = 0, ignored = 0;
                    string pattern = @".*Total-(\d+), Updated-(\d+), Failed-(\d+), Ignored-(\d+)";

                    foreach (var apiResponse in changePrimary)
                    {
                        Match match = Regex.Match(apiResponse.Message, pattern);

                        if (match.Success)
                        {
                            total += int.Parse(match.Groups[1].Value);
                            updated += int.Parse(match.Groups[2].Value);
                            failed += int.Parse(match.Groups[3].Value);
                            ignored += int.Parse(match.Groups[4].Value);
                        }
                    }

                    var recievedPSChangeCount = updatePrimaryCategories.Count();

                    string jobMessage = "Master APIs - Item Master Sync Completion Log- \r\n Product Hierarchy Update (Primary Cat <> Secondary Cat) -";

                    await _slackLogHelper.SendCustomSlackMessage(jobMessage, true, "", recievedPSChangeCount, updated, failed, ignored);
                }

                if (updateSecondaryCategories.Count > 0)
                {
                    fileLogger.WriteLine("Updating Secondary Categories for Products");

                    var changeSProduct = await fACategoriesService.ChangeCategoryForProduct(updateSecondaryCategories);

                    int total = 0, updated = 0, failed = 0, ignored = 0;
                    string pattern = @".*Total-(\d+), Updated-(\d+), Failed-(\d+), Ignored-(\d+)";

                    foreach (var apiResponse in changeSProduct)
                    {
                        Match match = Regex.Match(apiResponse.Message, pattern);

                        if (match.Success)
                        {
                            total += int.Parse(match.Groups[1].Value);
                            updated += int.Parse(match.Groups[2].Value);
                            failed += int.Parse(match.Groups[3].Value);
                            ignored += int.Parse(match.Groups[4].Value);
                        }
                    }

                    var recievedSPCount = updateSecondaryCategories.Count();

                    var jobMessage = "Master APIs - Item Master Sync Completion Log- \r\n Product Hierarchy Update (Secondary Cat <> SKUs) -";

                    await _slackLogHelper.SendCustomSlackMessage(jobMessage, true, "", recievedSPCount, updated, failed, ignored);
                }

                //ReactivateProducts
                if (reactivatedProducts.Count > 0)
                {
                    fileLogger.WriteLine("Reactivating Products");
                    var reactivatedProductList = await fAProductService.ReactivateProducts(reactivatedProducts);
                    var recievedReactivation = reactivatedProducts.Count();
                    var failedReactivation = reactivatedProductList.Where(x => !x.IsActive).Count();
                    var successReactivation = reactivatedProductList.Where(x => x.IsActive).Count();
                    var jobMessage = "Master APIs - Item Master Sync Completion Log- \r\n Product Hierarchy Update (Product Reactivation) -";
                    await _slackLogHelper.SendCustomSlackMessage(jobMessage, true, "", recievedReactivation, successReactivation, failedReactivation, 0);
                }
                //UpdateCompanyProducts
                if (updateProducts.Count > 0)
                {
                    fileLogger.WriteLine("Updating Products");
                    var updatedProductsList = await fAProductService.UpdateProducts(updateProducts);
                    var recievedProductCount = updateProducts.Count();
                    var successProductCount = updatedProductsList.Count(d => d.ResponseStatus == ResponseStatus.Success || d.ResponseStatus == ResponseStatus.Ignored);
                    var jobMessage = "Master APIs - Item Master Sync Completion Log- \r\n Product Hierarchy Update (Product Update) -";
                    await _slackLogHelper.SendCustomSlackMessage(jobMessage, true, "", recievedProductCount, successProductCount, updatedProductsList.Count - successProductCount, 0);
                    if (updatedProductsList.Count > 0)
                    {
                        const int batchSize = 500;
                        for (int i = 0; i < updatedProductsList.Count; i += batchSize)
                        {
                            List<UpdatedProduct> batch = updatedProductsList.Skip(i).Take(batchSize).ToList();
                            _ = hCCBAcknowlegementService.AcknowledgeItemMasterUpdateProduct(accessToken, batch);// Fire and forget
                        }
                    }
                }
            }
        }
        #endregion

        #region ConverToBool
        public static bool ConvertToBoolean(string value)
        {
            if (value == "Y")
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion
    }
}
