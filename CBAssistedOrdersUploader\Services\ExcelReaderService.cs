using CBAssistedOrdersUploader.Models;
using OfficeOpenXml;
using System.Globalization;
using System.IO;

namespace CBAssistedOrdersUploader.Services
{
    public class ExcelReaderService
    {
        public async Task<List<ExcelOrderData>> ReadExcelFileAsync(string filePath, IProgress<string> progress)
        {
            var orders = new List<ExcelOrderData>();
            
            progress?.Report("Reading Excel file...");
            
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            
            using var package = new ExcelPackage(new FileInfo(filePath));
            var worksheet = package.Workbook.Worksheets[0];
            
            if (worksheet == null)
            {
                throw new InvalidOperationException("No worksheet found in the Excel file.");
            }
            
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount <= 1)
            {
                throw new InvalidOperationException("Excel file appears to be empty or has no data rows.");
            }
            
            progress?.Report($"Found {rowCount - 1} data rows to process...");
            
            // Read header row to map columns
            var headers = new Dictionary<string, int>();
            for (int col = 1; col <= worksheet.Dimension.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim();
                if (!string.IsNullOrEmpty(header))
                {
                    headers[header.ToUpper()] = col;
                }
            }
            
            // Process data rows
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var order = new ExcelOrderData
                    {
                        S_No = GetLongValue(worksheet, row, headers, "S NO") ?? 0,
                        SALES_POINT_ID = GetStringValue(worksheet, row, headers, "SALES POINT ID"),
                        STL_ID = GetStringValue(worksheet, row, headers, "STL ID"),
                        STL_NAME = GetStringValue(worksheet, row, headers, "STL NAME"),
                        MARKET_AREA_NAME = GetStringValue(worksheet, row, headers, "MARKET AREA NAME"),
                        DISTRIBUTOR_ID = GetStringValue(worksheet, row, headers, "DISTRIBUTOR ID"),
                        OUTLET_ID = GetStringValue(worksheet, row, headers, "OUTLET ID"),
                        OUTLET_NAME = GetStringValue(worksheet, row, headers, "OUTLET NAME"),
                        ORDER_TYPE = GetStringValue(worksheet, row, headers, "ORDER TYPE"),
                        ORDER_ID_IN_CHATBOT = GetLongValue(worksheet, row, headers, "ORDER ID IN CHATBOT"),
                        ORDER_DATE = GetStringValue(worksheet, row, headers, "ORDER DATE"),
                        ORDER_TIME = GetStringValue(worksheet, row, headers, "ORDER TIME"),
                        Latitude = GetDoubleValue(worksheet, row, headers, "LATITUDE"),
                        Longitude = GetDoubleValue(worksheet, row, headers, "LONGITUDE"),
                        Ko_Week = GetIntValue(worksheet, row, headers, "KO WEEK"),
                        Ko_Month = GetIntValue(worksheet, row, headers, "KO MONTH"),
                        BUSINESS_UNIT = GetStringValue(worksheet, row, headers, "BUSINESS UNIT"),
                        DEVICE_ID = GetStringValue(worksheet, row, headers, "DEVICE ID")
                    };
                    
                    orders.Add(order);
                    
                    if (row % 1000 == 0)
                    {
                        progress?.Report($"Processed {row - 1} rows...");
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Error processing row {row}: {ex.Message}", ex);
                }
            }
            
            progress?.Report($"Successfully read {orders.Count} records from Excel file.");
            return orders;
        }
        
        private string? GetStringValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, string columnName)
        {
            if (!headers.TryGetValue(columnName, out int col))
                return null;
                
            var value = worksheet.Cells[row, col].Text?.Trim();
            return string.IsNullOrEmpty(value) || value.Equals("NA", StringComparison.OrdinalIgnoreCase) ? null : value;
        }
        
        private long? GetLongValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, string columnName)
        {
            var stringValue = GetStringValue(worksheet, row, headers, columnName);
            return long.TryParse(stringValue, out long result) ? result : null;
        }
        
        private int? GetIntValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, string columnName)
        {
            var stringValue = GetStringValue(worksheet, row, headers, columnName);
            return int.TryParse(stringValue, out int result) ? result : null;
        }
        
        private double? GetDoubleValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers, string columnName)
        {
            var stringValue = GetStringValue(worksheet, row, headers, columnName);
            return double.TryParse(stringValue, NumberStyles.Float, CultureInfo.InvariantCulture, out double result) ? result : null;
        }
    }
}
