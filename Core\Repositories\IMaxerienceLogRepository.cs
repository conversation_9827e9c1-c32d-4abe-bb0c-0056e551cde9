
using Core.Models;
using Core.Models.HccbDbModels;
using Core.Models.QueueModel;

namespace Core.Repositories
{
    public interface IMaxerienceLogRepository
    {
        Task<List<TruMindsSyncQueueData>> GetUnsyncedLogsAsync(long companyId);
        Task UpdateSyncStatusAsync(long logId, bool isSynced);
        Task<MaxerienceLog> GetLogAsync(long logId);
        Task<List<Scenes>> GetScenesForLogAsync(long logId, long companyId);

        Task<List<MaxerienceLogMin>> GetLogsByFilterAsync(long companyId,
            List<long> userIds,
            List<long> outletIds,
            long startDateKey,
            long endDateKey);
        Task<List<Scenes>> GetScenesByLogIdsAsync(List<long> logIds);
    }
}
