﻿using HCCB_ADLS.Core.Interfaces;
using HCCB_ADLS.Core.Models;
using HCCB_ADLS.DbStorage.DbContexts;
using HCCB_ADLS.DbStorage.MasterRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace HCCB_ADLS.DbStorage.MasterRepositories
{
    public class OutletRepository : IOutletRepository
    {

        private readonly MasterDbContext _db;

        public OutletRepository(MasterDbContext db)
        {
            _db = db;
        }

        #region Private

        private IQueryable<Location> GetOutlets(long companyId, bool includeBlocked = false)
        {
            if (includeBlocked)
                return _db.Locations.Where(s => s.CompanyId == companyId);
            return _db.Locations.Where(s => s.CompanyId == companyId && !s.IsBlocked);
        }

        #endregion Private


        public async Task<List<OutletList>> GetAllOutlets(long companyId, bool includeDeactivate = false)
        {
            var outletsQuerable = GetOutlets(companyId, includeDeactivate);
            return await outletsQuerable.Select(p => new OutletList()
            {
                Id = p.Id,
                Name = p.ShopName,
                ERPId = p.ErpId,
                AttributeText4 = p.AttributeText4
            }).AsNoTracking().ToListAsync();
        }


        public async Task<List<OutletLegacyUserErp>> GetOutletLegacyCodeAndUserERP(long companyId)
        {
            var result = (await (from l in _db.Locations
                                join rom in _db.RouteOutletMappings.Where(rom => !rom.Deleted && rom.CompanyId == companyId) on l.Id equals rom.LocationId
                                join rpi in _db.RoutePlanItems on rom.RouteId equals rpi.RouteId
                                join rp in _db.RoutePlans.Where(rp => !rp.Deleted && !rp.IsDeactive && rp.CompanyId == companyId) on rpi.RoutePlanId equals rp.Id
                                join ce in _db.ClientEmployees on rp.EmployeeId equals ce.Id
                                where l.CompanyId == companyId && !l.IsBlocked && ce.UserRole == Libraries.CommonEnums.PortalUserRole.ClientEmployee
                                select new
                                {
                                    OutletLegacyCode = l.AttributeText4,
                                    OutletId = l.Id,
                                    UserERPId = ce.ErpId,
                                    PlanStartDate = rp.StartDate,
                                }).AsNoTracking().ToListAsync()).GroupBy(d => d.OutletId).Select(g => g.OrderByDescending(d => d.PlanStartDate).First()).Select(d => new OutletLegacyUserErp
                                {
                                    OutletLegacyCode = d.OutletLegacyCode,
                                    OutletId = d.OutletId,
                                    UserERPId = d.UserERPId
                                }).ToList();
            return result;
        }

    }

}
