﻿{
  "AppSettings": {
    "Deployment": "dev",
    "NS_DataApiHost": "https://fasolutionv3-reporting-debug.azurewebsites.net/",
    "DataAPIToken": "56SbkbmV+#?p+dSNgGPNz8"
  },
  "ConnectionStrings": {
    "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=fadebug;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ReadOnlyTransactionDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=FA_Transactions_GT;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;",
    "ReadonlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;",
    "WritableTransactionDbConnectionString": "data source=ewwayxoov5.database.windows.net;initial catalog=FA_Transactions;user id=debug_write;password=****************************;"
  },
  "hccbAPIhelper": {
    "tokenUrl_UAT": "https://dm-em.informaticacloud.com/authz-service/oauth/token",
    "clientId_UAT": "5Elco2GNJ0rbU6QYPmvDmm",
    "clientSecret_UAT": "FVDIa7S06",
    "accessToken_UAT": "eyJraWQiOiI1RThUc2M5S0RacGJUOHNEdmtHSTI4IiwidHlwIjoiSldUIiwiYWxnIjoiUlMyNTYifQ.eyJzdWIiOiI1RWxjbzJHTkowcmJVNlFZUG12RG1tIiwiYXVkIjoiQVBJTSIsIm9yZ19pZCI6IjBpOE1FNjBTYlVMbFJwZ2J5cnpVdXkiLCJzY29wZSI6WyJleGVjdXRlIl0sImFkZGl0aW9uYWxfY2xhaW0iOiJleUpoYkd4QmNHbHpJanBtWVd4elpTd2laM0p2ZFhCeklqcGJJakoxWTNoeWRFcFpVRTF6WjBaWGRsTm5hVGhTUTFraVhTd2lkWE5sY2s1aGJXVWlPaUpUWVhWeVlXSm9JaXdpZFhObGNrbGtJam9pTVVvMmVEbEJVR0puYkVKa2F6QmtXbmRDYTFneVZTSjkiLCJpc3MiOiJodHRwczovL2RtLWVtLmluZm9ybWF0aWNhY2xvdWQuY29tL2F1dGh6LXNlcnZpY2UiLCJleHAiOjE3MzE1ODc3MDgsImNsaWVudF9uYW1lIjoiSENDQl9DVFNVc2VyIiwiaWF0IjoxNzMxNTg0MTA4LCJqdGkiOiJQcUxhRXRjZEtPdmpYdTdXdmc3R1ZqZVQxUjgiLCJjbGllbnRfaWQiOiI1RWxjbzJHTkowcmJVNlFZUG12RG1tIn0.bkZ7wdTKgHQ06P17u5z6pV6F-maeQhLfAdVSObtI9ZNX8xFH3zM817c9iTsOL-tjvFZmXfoi3g27EJuz5FMhQvVo9hN8566cePXmHHU9Vx66-9NqXdTWhbjN_NDniWa8ney4lbPW2_H95OcprOtOqwvl_17GV09QtOHAYXfg0whUC7Q1rwyLueS6r1emZM0jQLDEI8j5OmeyRwSZZm0qX9MXdxg49yzC9jUcJy5a6yuURuSchScl0MyvJGYJqhCtZp8uxqoLVBw77l4xZSF-rioF0UNAkJlYv5OhaNI_y2-vmt2IdUpYq6i15Vbwf-kmWhZH7vA60z4uWAuBbGtcMQ",
    "tokenUrl": "https://dm-em.informaticacloud.com/authz-service/oauth/token",
    "clientId": "2FSPmiSojgzgMikcTSQ50m",
    "clientSecret": "iy9beViCK",
    "accessToken": "MkZTUG1pU29qZ3pnTWlrY1RTUTUwbTppeTliZVZpQ0s=",
    "ctsStatusUrl_UAT": "https://emw1-apigw.dm-em.informaticacloud.com/t/0i8me60sbullrpgbyrzuuy.com/Prc_CAI_GetCTS_Status",
    "ctsStatusUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Prc_CAI_GetCTS_Status"
  }
}