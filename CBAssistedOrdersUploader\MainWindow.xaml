<Window x:Class="CBAssistedOrdersUploader.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="CB Assisted Orders Uploader" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title -->
        <TextBlock Grid.Row="0" Text="CB Assisted Orders Uploader" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- Connection String -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <Label Content="SQL Server Connection String:" FontWeight="Bold"/>
            <TextBox x:Name="ConnectionStringTextBox" Height="60" TextWrapping="Wrap" 
                     VerticalScrollBarVisibility="Auto"
                     Text="Server=localhost;Database=YourDatabase;Integrated Security=true;TrustServerCertificate=true;"/>
        </StackPanel>
        
        <!-- Table Name -->
        <StackPanel Grid.Row="2" Margin="0,0,0,15">
            <Label Content="Table Name:" FontWeight="Bold"/>
            <TextBox x:Name="TableNameTextBox" Height="25" Text="OrdersTable"/>
        </StackPanel>
        
        <!-- Excel File Selection -->
        <StackPanel Grid.Row="3" Margin="0,0,0,15">
            <Label Content="Excel File (.xlsx):" FontWeight="Bold"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox x:Name="ExcelFilePathTextBox" Grid.Column="0" Height="25" IsReadOnly="True"/>
                <Button x:Name="BrowseButton" Grid.Column="1" Content="Browse..." 
                        Width="80" Height="25" Margin="10,0,0,0" Click="BrowseButton_Click"/>
            </Grid>
        </StackPanel>
        
        <!-- Test Connection Button -->
        <Button x:Name="TestConnectionButton" Grid.Row="4" Content="Test Connection" 
                Width="150" Height="30" Margin="0,0,0,15" 
                HorizontalAlignment="Left" Click="TestConnectionButton_Click"/>
        
        <!-- Upload Button -->
        <Button x:Name="UploadButton" Grid.Row="5" Content="Upload Orders" 
                Width="150" Height="40" Margin="0,0,0,15" 
                HorizontalAlignment="Center" Click="UploadButton_Click"
                IsEnabled="False" FontWeight="Bold"/>
        
        <!-- Progress Bar -->
        <StackPanel Grid.Row="6" Margin="0,0,0,15">
            <Label x:Name="ProgressLabel" Content="Ready" FontWeight="Bold"/>
            <ProgressBar x:Name="ProgressBar" Height="20" Minimum="0" Maximum="100"/>
        </StackPanel>
        
        <!-- Log Output -->
        <StackPanel Grid.Row="7" Margin="0,0,0,15">
            <Label Content="Log Output:" FontWeight="Bold"/>
            <ScrollViewer VerticalScrollBarVisibility="Auto" Height="200">
                <TextBox x:Name="LogTextBox" IsReadOnly="True" TextWrapping="Wrap" 
                         VerticalAlignment="Stretch" Background="LightGray"/>
            </ScrollViewer>
        </StackPanel>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="8" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="Ready"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
