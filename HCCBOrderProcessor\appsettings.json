{"ConnectionStrings": {"WritableHccbDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;", "ReadOnlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;", "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;", "SlackStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;"}, "AppConfigSettings": {"TelegramChatId": "-*************", "TelegramBotToken": "**********************************************"}, "HccbTokenApi": {"Url": "https://dm-em.informaticacloud.com/authz-service/oauth/token", "Token": "amp3QWJvYlBsVWxseFpid2dqYWtjOTpKd3JsbEc1VWo="}, "HccbPageCountApi": {"Url": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Process_CAI_HCCB_EB2B_GetPageCount", "PartnerID": 1, "InterfaceID": 9}, "HccbOrderApi": {"Url": "https://emw1-apigw.dm-em.informaticacloud.com/t/9hrby9aujkudprm4k7otal.com/Process_CAI_GetSalesOrder", "PartnerID": 1}}