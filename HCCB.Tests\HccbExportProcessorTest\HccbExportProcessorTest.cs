using HCCB.Tests.Configurations;
using HccbExportProcessor.Configuration;
using HccbExportProcessor.Models;
using HccbExportProcessor.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HCCB.Tests.HccbExportProcessorTest
{
    [TestClass]
    public class HccbExportProcessorTest
    {
        private ServiceProvider serviceProvider;

        [TestInitialize]
        public void Initialise()
        {
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, configuration);
            serviceCollection.AddScoped<HccbExportService>();
            serviceProvider = serviceCollection.BuildServiceProvider();
        }


        [TestMethod]
        public async Task HccbExportProcessor()
        {
            var qp = serviceProvider.GetRequiredService<HccbExportService>();
            var data = new HccbExportQueueData()
            {
                ExportId = 8,
                CompanyId = 193054,
                ExportType = 0,
            };

            await qp.ProcessExportAsync(data);
        }
    }
}
