﻿
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HCCB_ADLS.DbStorage.MasterRepositories.HccbModels
{
    [Table("ADLSInvoice_YTD")]
    public class ADLSInvoice
    {
        public const int MaxLength = 1000;
        public long id { get; set; }
        public string distributor_legacy_code { get; set; }
        public string outlet_legacy_code { get; set; }
        public string orderno { get; set; }
        public string invoice_no { get; set; }
        public long invoice_line_no { get; set; }
        public DateTime invoice_date { get; set; }
        public TimeSpan invoice_time { get; set; }
        public string material { get; set; }
        public int totalqty_ea { get; set; }
        public double base_price { get; set; }
        public string mer_no { get; set; }
        public double discount_amount { get; set; }
        public double line_amount { get; set; }
        public double line_tax_on_discount { get; set; }
        public int sale_type { get; set; }
        public bool cancelled_invoice_flag { get; set; }
        public double ind_sgst_tax_rate { get; set; } // check data type
        public double ind_cgst_tax_rate { get; set; }
        public double ind_igst_tax_rate { get; set; }
        public double ind_utgst_tax_rate { get; set; }
        public double ind_cess_tax_rate { get; set; }
        public bool IsProcessed { get; set; }
        public long companyid { get; set; }
        public DateTime? createdAt { get; set; }
        public DateTime? updatedAt { get; set; }
        public string creationcontext { get; set; }
        public string FilePath { get; set; }
        [StringLength(MaxLength)]
        public string FAProcessFailures { get; set; }
        public int FAProcessAttempts { get; set; }
    }
}
