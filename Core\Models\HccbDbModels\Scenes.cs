﻿using EntityHelper;

namespace Core.Models.HccbDbModels
{
    public class Scenes : ICreatedEntity, IUpdatableEntity
    {
        public long Id { get; set; }

        public long MaxerienceLogId { get; set; }

        public long CompanyId { get; set; }

        public int SceneTypeCode { get; set; }

        public int SubSceneTypeCode { get; set; }

        public string? AssetId { get; set; }

        public DateTime CaptureTime { get; set; }

        public string? ImageSerialNumber { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string? CreationContext { get; set; }

        public string? ImageBlobPath { get; set; }
        public int? ImageGroupId { get; set; }

    }

}