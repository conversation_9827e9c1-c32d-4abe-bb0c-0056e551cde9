﻿using Core.Models.HccbDbModels;
using iTextSharp.text;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.draw;
using Library.StorageWriter.Reader_Writer;
using System.Globalization;
using HccbExportsProcessor.Constants;
using Libraries.CommonModels;
using Core.Models;

namespace HccbExportProcessor.Services
{
    public interface IPdfExportService
    {
        byte[] GenerateEmptyPdf(string message);
        Task<byte[]> GenerateExportPdfAsync(List<MaxerienceLogMin> logs, List<Scenes> scenes, Dictionary<long, EntityMinWithErp> userNames, Dictionary<long, EntityMinWithErp> outletNames);
    }

    public class PdfExportService : IPdfExportService
    {
        private const string FontPath = "wwwroot/fonts/arial.ttf";
        private readonly HCCBBlobReader _blobReader;
        private readonly Font _boldFont;
        private readonly Font _normalFont;


        public PdfExportService(HCCBBlobReader blobReader)
        {
            _blobReader = blobReader;
            _boldFont = FontFactory.GetFont(FontPath, BaseFont.IDENTITY_H, 11, Font.BOLD);
            _normalFont = FontFactory.GetFont(FontPath, BaseFont.IDENTITY_H, 11, Font.NORMAL);
        }

        public byte[] GenerateEmptyPdf(string message)
        {
            using var memoryStream = new MemoryStream();
            using var document = new Document(PageSize.A4, 25, 25, 30, 30);
            PdfWriter.GetInstance(document, memoryStream);
            document.Open();

            if (string.IsNullOrEmpty(message))
            {
                message = "No message provided";
            }

            AddCenteredMessage(document, message);
            document.Close();
            return memoryStream.ToArray();
        }

        public async Task<byte[]> GenerateExportPdfAsync(List<MaxerienceLogMin> logs, List<Scenes> scenes, Dictionary<long, EntityMinWithErp> userNames, Dictionary<long, EntityMinWithErp> outletNames)
        {
            if (logs == null || !logs.Any())
            {
                return GenerateEmptyPdf("No logs available to generate PDF");
            }

            using var memoryStream = new MemoryStream();
            using var document = new Document(PageSize.A4, 25, 25, 30, 30);
            PdfWriter.GetInstance(document, memoryStream);
            document.Open();

            foreach (var log in logs)
            {
                if (log != logs.First())
                {
                    AddSeparator(document);
                }

                AddLogHeader(document, log, userNames, outletNames);
                await AddScenes(document, log, scenes);
                document.Add(Chunk.NEWLINE);
            }

            document.Close();
            return memoryStream.ToArray();
        }

        #region PDF Section Builders

        private void AddCenteredMessage(Document document, string message)
        {
            var paragraph = new Paragraph(message, _normalFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 15f
            };
            document.Add(paragraph);
        }

        private void AddSeparator(Document document)
        {
            var line = new LineSeparator(1f, 100f, BaseColor.BLACK, Element.ALIGN_CENTER, -2);
            document.Add(new Chunk(line));
            document.Add(new Chunk(line));
            document.Add(Chunk.NEWLINE);
        }

        private void AddLogHeader(Document document, MaxerienceLogMin log, Dictionary<long, EntityMinWithErp> userNames, Dictionary<long, EntityMinWithErp> outletNames)
        {
            userNames.TryGetValue(log.UserId, out var user);
            outletNames.TryGetValue(log.OutletId, out var outlet);

            var header = new Paragraph { SpacingAfter = 0f };

            AddLabelValue(header, "MGR Name: ", user?.Name ?? $"{log.UserId}");
            AddLabelValue(header, "MA ID: ", user?.ErpId ?? $"{log.UserId}");
            AddLabelValue(header, "Outlet Name: ", outlet?.Name ?? $"{log.OutletId}");
            AddLabelValue(header, "Outlet SAP Code: ", outlet?.ErpId ?? $"{log.OutletId}");
            AddLabelValue(header, "Date of Visit: ", log.MaxerienceSessionStartTime.ToString("dd/MM/yyyy hh:mm:ss", CultureInfo.InvariantCulture));

            document.Add(header);
        }

        private async Task AddScenes(Document document, MaxerienceLogMin log, List<Scenes> scenes)
        {
            var sceneGroups = scenes.Where(s => s.MaxerienceLogId == log.Id).GroupBy(s => s.SceneTypeCode);

            foreach (var sceneGroup in sceneGroups)
            {
                string sceneName = SceneTypes.Types.TryGetValue(sceneGroup.Key, out var displayName) ? displayName : $"Scene {sceneGroup.Key}";
                var sceneHeader = new Paragraph { SpacingAfter = 2f };
                AddLabelValue(sceneHeader, "Scene: ", sceneName);
                document.Add(sceneHeader);
                await AddSubScenes(document, sceneGroup);
            }
        }

        private async Task AddSubScenes(Document document, IGrouping<int, Scenes> sceneGroup)
        {
            foreach (var subSceneGroup in sceneGroup.GroupBy(s => s.SubSceneTypeCode))
            {
                string subSceneName = GetSubSceneDisplayName(sceneGroup.Key, subSceneGroup.Key);
                var subSceneHeader = new Paragraph
                {
                    SpacingAfter = 2f,
                    IndentationLeft = 30f
                };
                AddLabelValue(subSceneHeader, "Sub-Scene: ", subSceneName);
                document.Add(subSceneHeader);

                await AddSubSceneImages(document, subSceneGroup.ToList());
            }
        }

        private async Task AddSubSceneImages(Document document, List<Scenes> subScenes)
        {
            var imagesParagraph = new Paragraph
            {
                SpacingBefore = 2f,
                IndentationLeft = 30f,
                SpacingAfter = 8f,
                Leading = 0,
                MultipliedLeading = 1.0f
            };

            bool hasImages = false;
            foreach (var scene in subScenes)
            {
                if (string.IsNullOrEmpty(scene.ImageBlobPath)) continue;
                try
                {
                    using var imageStream = await _blobReader.ReadDatBlobContentAsync(scene.ImageBlobPath);
                    if (imageStream == null) continue;

                    var image = Image.GetInstance(imageStream);

                    // Limit image width to 2.5 inches
                    const float maxWidthInPoints = 2.5f * 72;
                    if (image.Width > maxWidthInPoints)
                    {
                        image.ScaleToFit(maxWidthInPoints, maxWidthInPoints);
                    }

                    if (imagesParagraph.Count > 0)
                    {
                        imagesParagraph.Add(new Chunk(" ", _normalFont));
                    }

                    imagesParagraph.Add(new Chunk(image, 0, 0, true));
                    hasImages = true;
                }
                catch (Exception ex)
                {
                    if (imagesParagraph.Count > 0)
                    {
                        imagesParagraph.Add(new Chunk(" ", _normalFont));
                    }
                    var errorChunk = new Chunk($"[Error loading image: {ex.Message}]", FontFactory.GetFont(FontPath, BaseFont.IDENTITY_H, 10, Font.ITALIC, BaseColor.RED));
                    imagesParagraph.Add(errorChunk);
                    hasImages = true;
                }
            }

            if (hasImages)
            {
                document.Add(imagesParagraph);
            }
        }

        #endregion

        #region Helpers

        private void AddLabelValue(Paragraph paragraph, string label, string value)
        {
            paragraph.Add(new Chunk(label, _boldFont));
            paragraph.Add(new Chunk($"{value}\n", _normalFont));
        }

        private string GetSubSceneDisplayName(int sceneTypeCode, int subSceneCode)
        {
            return sceneTypeCode switch
            {
                1 => SceneTypes.CoolerSubTypes.TryGetValue(subSceneCode, out var cooler) ? cooler : $"Unknown Cooler Sub-Scene ({subSceneCode})",
                2 => SceneTypes.AmbientSubTypes.TryGetValue(subSceneCode, out var ambient) ? ambient : $"Unknown Ambient Sub-Scene ({subSceneCode})",
                3 => SceneTypes.PromotionalSubTypes.TryGetValue(subSceneCode, out var promo) ? promo : $"Unknown Promotional Sub-Scene ({subSceneCode})",
                _ => $"Unknown Sub-Scene ({subSceneCode}) for Scene Type ({sceneTypeCode})"
            };
        }

        #endregion
    }
}
