// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Concurrent;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Library.SlackService.Helpers;
using Library.SlackService.Interfaces;
using Microsoft.Extensions.Hosting;
using Polly;
using Microsoft.Extensions.Logging;
using Library.SlackService.Model;
using System.Net;

namespace Library.SlackService.Services;

public class SlackDirectLogger(IHttpClientFactory httpClientFactory, ILogger<SlackDirectLogger> logger, int maxSecondsToWaitBeforeRetry = 60) 
    : BackgroundService, ISlackLogger
{
    #region Fields and Properties
    private readonly HttpClient _slackWebHookClient = httpClientFactory.CreateClient(SlackLogHelperMethods.SlackWebHookClientName);
    private readonly ILogger<SlackDirectLogger> _logger = logger;
    private readonly BlockingCollection<SlackMessage> _entryQueue = new BlockingCollection<SlackMessage>(1024);
    private readonly ConcurrentQueue<SlackMessage> _backUpQueue = new ConcurrentQueue<SlackMessage>();
    private readonly AsyncPolicy<HttpResponseMessage> _retryPolicy = Policy
    .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
    .WaitAndRetryAsync(
        retryCount: 10,
        sleepDurationProvider: (retryAttempt, outcome, context) =>
        {
            var waitingTime = TimeSpan.FromSeconds(Math.Max(Math.Pow(2, retryAttempt), maxSecondsToWaitBeforeRetry));
            if (outcome.Result?.StatusCode == HttpStatusCode.TooManyRequests)
            {
                // Honor Retry-After if provided
                var retryAfter = outcome.Result.Headers.RetryAfter?.Delta;
                if (retryAfter.HasValue && retryAfter.Value > waitingTime)
                {
                    waitingTime = retryAfter.Value;
                }
            }

            return waitingTime;
        },
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
        onRetryAsync: async (outcome, timespan, retryCount, context) =>
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
        {
            logger.LogWarning($"Request failed with status {outcome.Result.StatusCode}. Retry attempt {retryCount} after {timespan.TotalSeconds}s");
        });
    #endregion Fields and Properties

    #region Public
    void ISlackLogger.LogInfo(string logMessage, object metaData)
    {
        try
        {
            var slackMessage = SlackLogHelperMethods.GetSlackMessage(logMessage, null, metaData, LogLevel.Information);
            EnqueueMessage(slackMessage);
        }
        catch (Exception loggingException)
        {
            _logger.LogError($"Failed to add log to queue. Error:\n{loggingException.Message}");
            _logger.LogError($"{logMessage}\n{JsonSerializer.Serialize(metaData)}");
        }
    }

    void ISlackLogger.LogError(string logMessage, Exception ex, object metaData)
    {
        try
        {
            var slackMessage = SlackLogHelperMethods.GetSlackMessage(logMessage, ex, metaData, LogLevel.Error);
            EnqueueMessage(slackMessage);
        }
        catch (Exception loggingException)
        {
            _logger.LogError($"Failed to add log to queue. Error:\n{loggingException.Message}");
            _logger.LogError($"{logMessage}\n{JsonSerializer.Serialize(metaData)}\n{ex}");
        }
    }
    #endregion Public

    #region Private
    private void EnqueueMessage(SlackMessage slackMessage)
    {
        if (_entryQueue.IsAddingCompleted)
        {
            _backUpQueue.Enqueue(slackMessage);
        }
        else
        {
            _entryQueue.Add(slackMessage);
        }
    }

    private async Task SendMessageWithRetry(SlackMessage message)
    {
        try
        {
            await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _slackWebHookClient.PostAsJsonAsync("", message);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(exception: ex, message: ex.Message);
        }
    }
    #endregion Private

    #region Override
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        const int msToWaitForInFlightMessages = 5000;
        _logger.LogInformation($"{GetType().FullName} background service is starting.");

        foreach (var message in _entryQueue.GetConsumingEnumerable())
        {

            await SendMessageWithRetry(message);

            if (stoppingToken.IsCancellationRequested && !_entryQueue.IsAddingCompleted)
            {
                _entryQueue.CompleteAdding();
            }
        }

        await Task.Delay(msToWaitForInFlightMessages, CancellationToken.None); // Give some time for any in-flight messages to be added to the backup queue

        while (_backUpQueue.TryDequeue(out var message))
        {
            await SendMessageWithRetry(message);
        }

        _logger.LogInformation($"SlackDirectLogger background service is stopping.");
        _entryQueue.Dispose();
    }
    #endregion Override
}
