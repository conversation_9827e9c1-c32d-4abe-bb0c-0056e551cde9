﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using SlackNet.Blocks;
using System.Collections.Generic;
using Library.SlackService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Library.SlackService.Interfaces;
using System.Linq;
using System.Text.Json;
using Library.SlackService.Model;
using Microsoft.Extensions.Logging;
using Library.CommonHelpers.Extensions;
using System.Net.Http;

namespace Library.SlackService.Helpers
{
    public static class SlackLogHelperMethods
    {
        #region Const and Static Fields
        internal const string SlackWebHookClientName = "SlackWebHookClient";
        private const int StrackTraceMaxLength = 1500;
        private const int MaxAttachmentTextLength = 7600;
        private const int MaxAttachmentCount = 100;
        private static readonly HashSet<string> s_attachmentsToAvoidCodeBlocksOn =
        [
            "LogMessage"
        ];
        #endregion Const and Static Fields

        #region DI
        public static void AddSlackDirectLogger(this IServiceCollection services, IConfiguration configuration, string webHookConfigKey = default, int maxSecondsToWaitBeforeRetry = 60)
        {
            webHookConfigKey = webHookConfigKey ?? "Slack:WebHook";
            var SlackWebHook = new Uri(configuration.GetValue<string>(webHookConfigKey));
            services.AddHttpClient(SlackWebHookClientName, c => c.BaseAddress = SlackWebHook);
            services.AddSingleton<ISlackLogger, SlackDirectLogger>(s =>
            {
                return new SlackDirectLogger(
                    s.GetRequiredService<IHttpClientFactory>(),
                    s.GetRequiredService<ILogger<SlackDirectLogger>>(),
                    maxSecondsToWaitBeforeRetry);
            });
            // Register the same singleton also as a hosted service
            services.AddHostedService(provider =>
                (SlackDirectLogger)provider.GetRequiredService<ISlackLogger>());
        }
        #endregion DI

        #region Slack Message Builders
        public static IList<TextObject> GetFieldsForException(Exception ex)
        {
            var stackTrace = ex.StackTrace != null && ex.StackTrace.Length > StrackTraceMaxLength
                        ? string.Concat("...\n", ex.StackTrace.AsSpan(ex.StackTrace.Length - 1 - StrackTraceMaxLength, StrackTraceMaxLength))
                        : ex.StackTrace;

            var fields = new List<TextObject>(3)
            {
                new Markdown($"*Message:*\n```{ex.Message}```"),
                new Markdown($"*Base Message:*\n```{ex.GetBaseException().Message}```"),
                new Markdown($"*StackTrace:*\n```{stackTrace}```")
            };

            return fields;
        }

        public static IList<TextObject> GetFieldsForMetaData(object metaData)
        {
            if (metaData == null)
            {
                return new List<TextObject>();
            }

            var props = metaData.GetType().GetProperties();
            var fields = new List<TextObject>(props.Count()); 
            foreach (var prop in props)
            {
                var value = prop.GetValue(metaData);
                if (value != null)
                {
                    fields.Add(new Markdown($"*{prop.Name}:*\n{value}"));
                }
            }

            return fields;
        }

        internal static Attachment GetAttachmentForException(Exception ex, string logColor)
        {
            var attachment = new Attachment
                            {
                                Color = logColor,
                                Title = "Exception Details",
                                Text = ex.ToString(),
                            };
            return attachment;
        }

        internal static Attachment GetAttachmentForMetaData(object metaData, string logColor)
        {
            var attachment = new Attachment
            {
                Color = logColor,
                Title = "MetaData",
                Text = JsonSerializer.Serialize(metaData),
            };
            return attachment;
        }

        internal static SlackMessage GetSlackMessage(string logMessage, Exception ex, object metaData, LogLevel logLevel)
        {
            var logColor = GetLogLevelColor(logLevel);
            var slackMessage = new SlackMessage
            {
                Attachments = new List<Attachment>
                {
                    new Attachment
                    {
                        Color = logColor,
                        Title = "LogMessage",
                        Text = logMessage,
                    }
                }
            };

            if (metaData != null)
            {
                slackMessage.Attachments.Add(GetAttachmentForMetaData(metaData, logColor));
            }

            if (ex != null)
            {
                slackMessage.Attachments.Add(GetAttachmentForException(ex, logColor));
            }

            SplitAttachments(slackMessage);

            return slackMessage;
        }

        private static void SplitAttachments(SlackMessage slackMessage)
        {
            var finalAttachments = new List<Attachment>();
            foreach (var attachment in slackMessage.Attachments)
            {
                var totalLength = attachment.Title.Length + attachment.Text.Length;
                if (totalLength > MaxAttachmentTextLength)
                {
                    var textParts = attachment.Text.GetBatches(MaxAttachmentTextLength).Select(s => new string(s.ToArray()));
                    var partNumber = 0;
                    foreach (var textPart in textParts)
                    {
                        finalAttachments.Add(new Attachment
                        {
                            Title = attachment.Title + $" ----Part {partNumber++}----",
                            Text = s_attachmentsToAvoidCodeBlocksOn.Contains(attachment.Title) ? textPart : $"```{textPart}```",
                        });
                    }
                }
                else
                {
                    attachment.Text = s_attachmentsToAvoidCodeBlocksOn.Contains(attachment.Title) ? attachment.Text : $"```{attachment.Text}```";
                    finalAttachments.Add(attachment);
                }
            }

            if(finalAttachments.Count > MaxAttachmentCount)
            {
                Console.WriteLine($"[SlackLogHelperMethods]: ❌ {finalAttachments.Count} Attachments after Splitting. Only {MaxAttachmentCount} are allowed!! Skipping the rest Original Attachments Count was: {slackMessage.Attachments.Count}");
                finalAttachments = finalAttachments.Take(MaxAttachmentCount - 1).ToList();
            }

            slackMessage.Attachments = finalAttachments;
        }

        private static string GetLogLevelColor(LogLevel logLevel)
        {
            switch (logLevel)
            {
                case LogLevel.Trace:
                case LogLevel.Debug:
                case LogLevel.Information:
                    return "#327ba8";
                case LogLevel.Warning:
                    return "#b8572e";
                case LogLevel.Error:
                case LogLevel.Critical:
                    return "danger";
                case LogLevel.None:
                default:
                    return "#4f4f4f";
            }
        }
        #endregion Slack Message Builders
    }
}
