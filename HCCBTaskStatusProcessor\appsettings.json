{"CompanyId": 193017, "ConnectionStrings": {"HCCBStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=betahccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "WritableHccbDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;", "ReadOnlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;", "ReadOnlyTransactionDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=FA_Transactions_GT;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;", "SlackStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;"}, "AppConfigSettings": {"TelegramChatId": "-*************", "TelegramBotToken": "**********************************************"}}