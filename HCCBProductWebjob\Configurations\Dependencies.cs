﻿using Core.Loggers;
using HCCBProductWebjob.DbContexts;
using HCCBProductWebjob.Repositories;
using HCCBProductWebjob.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Headers;
using Telegram.Bot;
using Core.APIHelpers;
using SlackNet;
using SlackNet.Extensions.DependencyInjection;
using HCCBProductWebjob.Services.Interfaces;
using HCCB.Core.Helpers;
using HCCBProductWebjob.Processors;
using HCCBProductWebjob.Helpers;
using Core.Repositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using HCCB.DbStorage.DbContexts;

namespace HCCBProductWebjob.Configurations
{
    public static class Dependencies
    {
        public static string ChannelId = "C08D4PSBW4V";
        public static long CompanyId;
        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {
            ChannelId = configuration.GetValue<string>("Slack:ChannelId") ?? ChannelId;
            CompanyId = configuration.GetValue<long>("CompanyId");
            // AppConfigSettings from config
            var configSettings = new AppConfigSettings
            {
                TelegramBotToken = configuration.GetValue<string>("Values:TelegramBotToken"),
                HCCBClientAPIToken = configuration.GetValue<string>("Values:HCCBClientAPIToken"),
                APIBasePath = configuration.GetValue<string>("Values:APIBasePath"),
                ExtApiBasePath = configuration.GetValue<string>("Values:ExtApiBasePath"),
            };

            // HttpClients
            serviceProvider.AddHttpClient("ExternalApiClient", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
            });
            serviceProvider.AddHttpClient("FAExternalApiClient", client =>
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", configSettings.HCCBClientAPIToken);
                client.Timeout = TimeSpan.FromSeconds(30);
            });
            serviceProvider
            .AddDbContext<HCCBFaDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("HCCBFaDataConnectionString"), SqlResiliencyBuilder);
            });
            // Add Master Data Reader
            serviceProvider.AddSingleton<MasterDbSqlDataReader>(sp =>
            {
                var connectionString = configuration.GetConnectionString("ReadOnlyMasterDbConnectionString");
                return new MasterDbSqlDataReader(connectionString, timeout: 500);
            });

            // DI registrations
            serviceProvider.AddScoped<IHCCBGetItemsService, HCCBGetItemsService>();
            serviceProvider.AddScoped<IFACategoriesService, FACategoriesService>();
            serviceProvider.AddScoped<IPickListMasterRepository, PickListMasterRepository>();
            serviceProvider.AddScoped<IHCCBGetIPCMasterService, HCCBGetIPCMasterService>();
            serviceProvider.AddScoped<IPageNumberService, PageNumberService>();
            serviceProvider.AddScoped<IProductGroupService, ProductGroupService>();
            serviceProvider.AddScoped<IGetOAuthTokenService, GetOAuthTokenService>();
            serviceProvider.AddScoped<IHCCBAcknowlegementService, HCCBAcknowlegementService>();
            serviceProvider.AddScoped<IFAProductService, FAProductService>();

            serviceProvider.AddScoped<APICallHelper>();
            serviceProvider.AddScoped<HttpRequestHelper>();
            serviceProvider.AddScoped<FileLogger>();
            serviceProvider.AddScoped<HttpClientHandler>();
            serviceProvider.AddSingleton(e => configSettings);

            //Repo
            serviceProvider.AddScoped<IProductGroupsRepository, ProductGroupsRepository>();

            //Telegram Bot
            serviceProvider.AddSingleton<ITelegramBotClient>(d => new TelegramBotClient(configSettings.TelegramBotToken));

            serviceProvider.AddHttpClient().ConfigureHttpClientDefaults(c =>
                c.ConfigureHttpClient(client => client.Timeout = TimeSpan.FromMinutes(10)));

            //Slack
            var slackBotToken = configuration["Slack:BotToken"];         
            serviceProvider.AddSlackNet(c => c.UseApiToken(slackBotToken));

            var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");

            serviceProvider.AddSingleton<ISlackLogHelper>(sp => new SlackLogHelperV2(sp.GetRequiredService<IHttpClientFactory>(),
                sp.GetRequiredService<ISlackApiClient>(), masterStorageConnectionString, ChannelId));


            // HCCB API values from config/env
            var hccbApiValues = new HCCBApiValues
            {
                Username = configuration.GetValue<string>("hccbAPIhelper:Username"),
                Password = configuration.GetValue<string>("hccbAPIhelper:Password"),
                PartnerId = configuration.GetValue<int>("hccbAPIhelper:PartnerId"),
                HccbBaseUrl = configuration.GetValue<string>("hccbAPIhelper:HccbBaseUrl"),
                IpcInterfaceId = configuration.GetValue<int>("hccbAPIhelper:IpcInterfaceId"),
                ItemInterfaceId = configuration.GetValue<int>("hccbAPIhelper:ItemInterfaceId"),
                StartDate = DateTime.UtcNow.AddMinutes(330).Date.AddDays(-1),
                EndDate = DateTime.UtcNow.AddMinutes(330).Date.AddDays(+1),
            };
            serviceProvider.AddSingleton(d => hccbApiValues);

            serviceProvider.AddScoped<AlternateCategoryProcessor>();
            serviceProvider.AddScoped<ProductProcessor>();
        }
        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.EnableRetryOnFailure(3,
                TimeSpan.FromSeconds(30),
                null);
        }
    }
}
