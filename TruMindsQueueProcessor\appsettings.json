﻿{
  "ConnectionStrings": {
    "WritableHccbDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;",
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=betahccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ReadonlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;"
  },
  "Slack": {
    "WebHook": "*******************************************************************************",
    "ApiToken": "xoxb-**********-*************-FISHBycTTu9YUPWuy3nwBVlR",
    "ChannelId": "C08D4PSBW4V"
  },
  "TruMinds": {
    "ApiBaseUrl": "https://storeanalysis.truminds.co.in",
    "UploadEndpoint": "/sav2/api/upload",
    "UserName": "hccb_sfa",
    "Password": "MpT@]0YLraz8J!0",
    "LoginEndpoint": "/sav2/api/login"
  },
  "CompanyId": 193017
}